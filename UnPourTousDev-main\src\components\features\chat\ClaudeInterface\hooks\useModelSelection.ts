import { useState, useEffect, useCallback } from "react";
import { useQuery } from "convex/react";
import { api } from "../../../../../../convex/_generated/api";
import { AIModel } from "../types";

// Constante pour l'ID AutoSelect
export const AUTOSELECT_ID = "openrouter/auto";

/**
 * Hook personnalisé pour gérer la sélection des modèles
 * Gère la sélection des modèles, l'état useAutoRouter et les catégories
 */
export const useModelSelection = () => {
  // État principal pour la sélection active (modèle ou AutoSelect)
  const [activeSelection, setActiveSelection] = useState<string>(AUTOSELECT_ID);

  // États existants pour la compatibilité avec le reste de l'application
  const [selectedModel, setSelectedModel] = useState(AUTOSELECT_ID);
  const [useAutoRouter, setUseAutoRouter] = useState(true);

  // État pour la catégorie ouverte
  const [openCategory, setOpenCategory] = useState<string | null>(null);

  // Récupérer la liste des modèles depuis Convex
  const models = useQuery(api.livemodels.list) || [];

  // Debug: afficher les modèles récupérés
  console.log("[useModelSelection] Modèles récupérés:", models);
  console.log("[useModelSelection] Nombre de modèles:", models.length);
  if (models.length > 0) {
    console.log("[useModelSelection] Premier modèle:", models[0]);
    console.log("[useModelSelection] Structure du premier modèle:", Object.keys(models[0]));
  }

  // Fonction pour sélectionner AutoSelect
  const handleAutoSelect = useCallback(() => {
    setActiveSelection(AUTOSELECT_ID);
  }, []);

  // Fonction pour sélectionner un modèle spécifique
  const handleModelSelect = useCallback((modelId: string) => {
    setActiveSelection(modelId);
  }, []);

  // Synchroniser activeSelection avec les états existants
  useEffect(() => {
    const isAutoSelect = activeSelection === AUTOSELECT_ID;
    setUseAutoRouter(isAutoSelect);
    setSelectedModel(activeSelection);
  }, [activeSelection]);

  // Fonction pour basculer l'état d'une catégorie
  const toggleCategory = useCallback((category: string) => {
    if (openCategory === category) {
      setOpenCategory(null); // Fermer si déjà ouvert
    } else {
      setOpenCategory(category); // Ouvrir cette catégorie
    }
  }, [openCategory]);

  // Filtrer les modèles par catégorie
  const chatModels = models.filter((model: AIModel) => model.chat === true && model.enabled !== false);
  const webSearchModels = models.filter((model: AIModel) => model.webSearch === true && model.enabled !== false);
  const reasoningModels = models.filter((model: AIModel) => model.reasoning === true && model.enabled !== false);

  console.log("[useModelSelection] Modèles chat:", chatModels);
  console.log("[useModelSelection] Modèles webSearch:", webSearchModels);
  console.log("[useModelSelection] Modèles reasoning:", reasoningModels);

  return {
    // Nouveaux états et fonctions
    activeSelection,
    handleAutoSelect,
    handleModelSelect,

    // États et fonctions existants pour la compatibilité
    selectedModel,
    setSelectedModel: handleModelSelect, // Remplacer par handleModelSelect
    useAutoRouter,
    setUseAutoRouter: (use: boolean) => {
      if (use) handleAutoSelect();
    },
    openCategory,
    toggleCategory,
    models,
    chatModels,
    webSearchModels,
    reasoningModels
  };
};
