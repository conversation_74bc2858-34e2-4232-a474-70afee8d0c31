import { useState, useEffect, useCallback } from "react";
import { useQuery } from "convex/react";
import { api } from "../../../../../../convex/_generated/api";
import { AIModel } from "../types";

// Constante pour l'ID AutoSelect
export const AUTOSELECT_ID = "openrouter/auto";

/**
 * Hook personnalisé pour gérer la sélection des modèles
 * Gère la sélection des modèles, l'état useAutoRouter et les catégories
 */
export const useModelSelection = () => {
  // État principal pour la sélection active (modèle ou AutoSelect)
  const [activeSelection, setActiveSelection] = useState<string>(AUTOSELECT_ID);

  // États existants pour la compatibilité avec le reste de l'application
  const [selectedModel, setSelectedModel] = useState(AUTOSELECT_ID);
  const [useAutoRouter, setUseAutoRouter] = useState(true);

  // État pour la catégorie ouverte
  const [openCategory, setOpenCategory] = useState<string | null>(null);

  // Récupérer la liste des modèles depuis Convex
  const models = useQuery(api.livemodels.list) || [];

  // Fonction pour sélectionner AutoSelect
  const handleAutoSelect = useCallback(() => {
    setActiveSelection(AUTOSELECT_ID);
  }, []);

  // Fonction pour sélectionner un modèle spécifique
  const handleModelSelect = useCallback((modelId: string) => {
    setActiveSelection(modelId);
  }, []);

  // Synchroniser activeSelection avec les états existants
  useEffect(() => {
    const isAutoSelect = activeSelection === AUTOSELECT_ID;
    setUseAutoRouter(isAutoSelect);
    setSelectedModel(activeSelection);
  }, [activeSelection]);

  // Fonction pour basculer l'état d'une catégorie
  const toggleCategory = useCallback((category: string) => {
    if (openCategory === category) {
      setOpenCategory(null); // Fermer si déjà ouvert
    } else {
      setOpenCategory(category); // Ouvrir cette catégorie
    }
  }, [openCategory]);

  // Filtrer les modèles par catégorie
  const chatModels = models.filter((model: AIModel) => model.chat === true);
  const webSearchModels = models.filter((model: AIModel) => model.webSearch === true);
  const reasoningModels = models.filter((model: AIModel) => model.reasoning === true);

  return {
    // Nouveaux états et fonctions
    activeSelection,
    handleAutoSelect,
    handleModelSelect,

    // États et fonctions existants pour la compatibilité
    selectedModel,
    setSelectedModel: handleModelSelect, // Remplacer par handleModelSelect
    useAutoRouter,
    setUseAutoRouter: (use: boolean) => {
      if (use) handleAutoSelect();
    },
    openCategory,
    toggleCategory,
    models,
    chatModels,
    webSearchModels,
    reasoningModels
  };
};
