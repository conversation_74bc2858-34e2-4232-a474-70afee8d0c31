/* Loader animation for the Comfy interface */
.loader {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: inline-block;
  position: relative;
  border: 2px solid #e37313;
  animation: rotation 1s linear infinite;
}

.loader:after {
  content: '';
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: 2px solid;
  border-color: #5D5CDE transparent;
}

@keyframes rotation {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Custom scrollbar styles */
.comfy-interface ::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.comfy-interface ::-webkit-scrollbar-track {
  background: transparent;
}

.comfy-interface ::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.15);
  border-radius: 3px;
}

.comfy-interface ::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}

.comfy-interface.dark ::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.15);
}

.comfy-interface.dark ::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* Font styles */
.comfy-interface h1 {
  font-family: var(--font-display);
  font-weight: 400; /* Regular font weight for the main heading */
}

.comfy-interface textarea {
  font-family: inherit;
}

/* Ensure smooth transitions */
.comfy-interface button {
  transition: all 0.2s ease;
}

/* Add some letter spacing to the heading */
.comfy-interface h1 {
  letter-spacing: 0.01em;
  line-height: 1.2;
}

/* Ensure proper textarea behavior */
.comfy-interface textarea {
  line-height: 1.5;
  overflow-y: hidden; /* Hide scrollbar when not needed */
}

/* Interactive model selector styles */
.hover\:scale-102:hover {
  transform: scale(1.02);
}

.scale-105 {
  transform: scale(1.05);
}

/* Smooth backdrop blur effect */
.backdrop-blur-sm {
  backdrop-filter: blur(4px);
}

/* Enhanced shadow effects */
.shadow-claude-orange\/25 {
  box-shadow: 0 10px 25px -3px rgba(227, 115, 19, 0.25), 0 4px 6px -2px rgba(227, 115, 19, 0.1);
}

/* Gradient animation for shine effect */
@keyframes shine {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}


