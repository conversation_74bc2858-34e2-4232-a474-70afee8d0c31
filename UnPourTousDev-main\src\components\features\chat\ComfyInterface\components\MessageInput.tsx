import React, { useRef, useEffect } from "react";
import { MessageInputProps } from "../types";
import { useModelSelection } from "../hooks/useModelSelection";
import CategoryButtons from "./CategoryButtons";
import ModelSelector from "./ModelSelector";
import logo from "../../../../../assets/logo3.svg";

/**
 * Composant MessageInput pour l'interface Comfy
 * Gère la saisie des messages, l'affichage des boutons de catégorie et la sélection des modèles
 */
const MessageInput: React.FC<MessageInputProps> = ({
  userMessage,
  setUserMessage,
  isLoading,
  handleKeyDown,
  startChat,
  selectedModel,
  setSelectedModel,
  useAutoRouter,
  setUseAutoRouter
}) => {
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Récupérer les fonctions de gestion des catégories depuis le hook useModelSelection
  const {
    openCategory,
    toggleCategory,
    models,
    activeSelection,
    handleAutoSelect,
    handleModelSelect
  } = useModelSelection();

  // Auto-resize du textarea
  useEffect(() => {
    if (textareaRef.current) {
      const adjustHeight = () => {
        const textarea = textareaRef.current;
        if (textarea) {
          textarea.style.height = 'auto';
          const newHeight = Math.min(textarea.scrollHeight, 300);
          textarea.style.height = `${newHeight}px`;
        }
      };

      adjustHeight();
      const currentTextarea = textareaRef.current;
      currentTextarea.addEventListener('input', adjustHeight);

      return () => {
        if (currentTextarea) {
          currentTextarea.removeEventListener('input', adjustHeight);
        }
      };
    }
  }, [userMessage]);

  // Fonction pour démarrer le chat avec le modèle sélectionné
  const handleStartChat = () => {
    const modelId = activeSelection === "autoselect" ? "openrouter/auto" : activeSelection;
    startChat(modelId);
  };

  return (
    <div className="p-4 border-t border-comfy-light-gray">
      <div className="max-w-3xl mx-auto">
        {/* Sélecteur de modèle (visible uniquement si une catégorie est ouverte) */}
        {openCategory && (
          <div className="mb-4">
            <ModelSelector
              models={models}
              selectedModel={activeSelection}
              setSelectedModel={handleModelSelect}
              openCategory={openCategory}
            />
          </div>
        )}

        {/* Zone de saisie */}
        <div className="relative w-full rounded-xl border border-comfy-light-gray bg-comfy-gray p-2 shadow-sm">
          <textarea
            ref={textareaRef}
            value={userMessage}
            onChange={(e) => setUserMessage(e.target.value)}
            onKeyDown={handleKeyDown}
            className="w-full bg-transparent outline-none resize-none text-base py-2 px-3 h-12 max-h-60 font-body text-white placeholder-gray-400"
            placeholder="Comment puis-je vous aider ?"
            rows={1}
          />
          <div className="flex justify-between px-2 pt-1">
            <div className="flex items-center">
              <button type="button" className="p-1.5 rounded-lg text-gray-400 hover:bg-comfy-light-gray/30 hover:text-white">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-5 h-5">
                  <path strokeLinecap="round" strokeLinejoin="round" d="M12 4.5v15m7.5-7.5h-15" />
                </svg>
              </button>
              <button type="button" className="p-1.5 rounded-lg text-gray-400 hover:bg-comfy-light-gray/30 hover:text-white">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-5 h-5">
                  <path strokeLinecap="round" strokeLinejoin="round" d="M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25m0 12.75h7.5m-7.5 3H12M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 00-9-9z" />
                </svg>
              </button>
            </div>
            <div className="flex items-center gap-3">
              {/* Bouton AutoRouter */}
              <button
                type="button"
                onClick={handleAutoSelect}
                className={`p-1.5 rounded-lg text-white transition-all ${
                  activeSelection === "autoselect"
                    ? "bg-comfy-primary opacity-100 shadow-md"
                    : "bg-comfy-primary opacity-60 hover:opacity-80"
                }`}
                title="AutoRouter - Sélection automatique du meilleur modèle"
              >
                <img src={logo} alt="UnPourTous Logo" className="w-5 h-5" />
              </button>
              
              {/* Bouton d'envoi */}
              <button
                type="button"
                onClick={handleStartChat}
                disabled={!userMessage.trim() || isLoading}
                className="p-1.5 text-white rounded-lg bg-comfy-orange opacity-80 hover:opacity-100 disabled:opacity-50 transition-all"
              >
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-5 h-5">
                  <path strokeLinecap="round" strokeLinejoin="round" d="M6 12L3.269 3.126A59.768 59.768 0 0121.485 12 59.77 59.77 0 013.27 20.876L5.999 12zm0 0h7.5" />
                </svg>
              </button>
            </div>
          </div>
        </div>

        {/* Boutons de catégories */}
        <CategoryButtons
          openCategory={openCategory}
          toggleCategory={toggleCategory}
        />
      </div>
    </div>
  );
};

export default MessageInput;
