import { StrictMode } from "react";
import ReactDOM from "react-dom/client";
import { Clerk<PERSON><PERSON><PERSON>, useAuth } from "@clerk/clerk-react";
import { ConvexProviderWithClerk } from "convex/react-clerk";
import { ConvexReactClient } from "convex/react";
import App from "./App";
import "./index.css";
import "./styles/newConversationDemo.css";
import "./styles/claudeInterface.css";
import "./styles/fonts.css";
import { clerkConfig } from "./config/clerk-config";
import { dark } from "@clerk/themes";
import { ThemeProvider } from "./contexts/ThemeContext";

// Initialisation du client Convex
const convex = new ConvexReactClient(import.meta.env.VITE_CONVEX_URL as string);
const publishableKey = import.meta.env.VITE_CLERK_PUBLISHABLE_KEY as string;

// Utiliser le thème sombre par défaut pour Clerk
const prefersDarkMode = true;

ReactDOM.createRoot(document.getElementById("root")!).render(
  <StrictMode>
    <ThemeProvider>
      <ClerkProvider
        publishableKey={publishableKey}
        appearance={{
          ...clerkConfig.appearance,
          baseTheme: prefersDarkMode ? dark : undefined
        }}
        // Activation de la persistance de session
        tokenCache={(tokens) => {
          // Stockage des tokens dans le localStorage
          tokens.map((token) => {
            try {
              localStorage.setItem(`clerk-${token.name}`, token.value);
            } catch (e) {
              console.error("Failed to store token in localStorage", e);
            }
          });

          // Récupération des tokens depuis le localStorage
          return {
            getToken: (key) => {
              try {
                return localStorage.getItem(`clerk-${key}`);
              } catch (e) {
                console.error("Failed to get token from localStorage", e);
                return null;
              }
            },
            clearToken: (key) => {
              try {
                localStorage.removeItem(`clerk-${key}`);
              } catch (e) {
                console.error("Failed to clear token from localStorage", e);
              }
            }
          };
        }}
      >
        <ConvexProviderWithClerk client={convex} useAuth={useAuth}>
          <App />
        </ConvexProviderWithClerk>
      </ClerkProvider>
    </ThemeProvider>
  </StrictMode>
);
