import React from "react";
import { MessageHeaderProps } from "../types";
import { ModelIcon } from "../../../../features/models";

/**
 * Composant pour l'en-tête d'un message IA
 */
const MessageHeader: React.FC<MessageHeaderProps> = ({
  modelName,
  modelUsed,
  isWebSearchModel,
  isAutoRouter
}) => {
  return (
    <div className="flex items-center mb-1">
      <span className="text-xs font-medium text-gray-500 dark:text-gray-400 flex items-center">
        <ModelIcon
          model={isAutoRouter ? "openrouter/auto" : modelUsed}
          provider={isAutoRouter ? "openrouter" : undefined}
          size={16}
          className="mr-1.5"
        />
        IA ({modelName})
        {isWebSearchModel && (
          <span className="ml-1 flex items-center text-xs text-blue-500 dark:text-blue-400 bg-blue-100 dark:bg-blue-900 px-1.5 py-0.5 rounded-full">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9" />
            </svg>
            recherche web
          </span>
        )}
      </span>
    </div>
  );
};

export default MessageHeader;
