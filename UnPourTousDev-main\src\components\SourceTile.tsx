import React, { useState } from 'react';

interface SourceTileProps {
  source: {
    id: string;
    title: string;
    url: string;
  };
}

export default function SourceTile({ source }: SourceTileProps) {
  const [isHovered, setIsHovered] = useState(false);
  // Fonction pour extraire le nom de domaine à partir de l'URL
  const extractDomain = (url: string) => {
    try {
      const domain = new URL(url).hostname;
      return domain.startsWith('www.') ? domain.substring(4) : domain;
    } catch (e) {
      return url;
    }
  };

  // Fonction pour extraire un nom convivial à partir de l'URL
  const extractFriendlyName = (url: string) => {
    const domain = extractDomain(url);

    // Cas spécifiques pour les sites populaires
    if (domain.includes('wikipedia')) {
      return 'Wikipedia';
    }
    if (domain.includes('youtube') || domain.includes('youtu.be')) {
      return 'YouTube';
    }
    if (domain.includes('philomag')) {
      return 'Philomag';
    }
    if (domain.includes('twitter') || domain.includes('x.com')) {
      return 'Twitter';
    }
    if (domain.includes('facebook')) {
      return 'Facebook';
    }
    if (domain.includes('instagram')) {
      return 'Instagram';
    }
    if (domain.includes('linkedin')) {
      return 'LinkedIn';
    }
    if (domain.includes('github')) {
      return 'GitHub';
    }
    if (domain.includes('medium')) {
      return 'Medium';
    }
    if (domain.includes('reddit')) {
      return 'Reddit';
    }
    if (domain.includes('quora')) {
      return 'Quora';
    }
    if (domain.includes('amazon')) {
      return 'Amazon';
    }
    if (domain.includes('google')) {
      return 'Google';
    }

    // Pour les autres sites, extraire le nom du domaine principal
    const parts = domain.split('.');
    if (parts.length >= 2) {
      // Prendre la partie avant le TLD (.com, .fr, etc.)
      const name = parts[parts.length - 2];
      // Mettre la première lettre en majuscule
      return name.charAt(0).toUpperCase() + name.slice(1);
    }

    // Fallback
    return domain;
  };

  // Fonction pour générer une description simple du site
  const generateDescription = (url: string, title: string) => {
    const domain = extractDomain(url);
    const friendlyName = extractFriendlyName(url);

    // Descriptions spécifiques pour les sites populaires
    if (domain.includes('wikipedia')) {
      return 'Encyclopédie en ligne collaborative';
    }
    if (domain.includes('youtube')) {
      return 'Plateforme de partage de vidéos';
    }
    if (domain.includes('philomag')) {
      return 'Magazine de philosophie';
    }
    if (domain.includes('twitter') || domain.includes('x.com')) {
      return 'Réseau social de microblogging';
    }
    if (domain.includes('facebook')) {
      return 'Réseau social';
    }
    if (domain.includes('github')) {
      return 'Plateforme de développement collaboratif';
    }
    if (domain.includes('medium')) {
      return 'Plateforme de publication d\'articles';
    }
    if (domain.includes('reddit')) {
      return 'Agrégateur de contenus et forum de discussion';
    }
    if (domain.includes('hostinger')) {
      return 'Plateforme d\'hébergement web et de tutoriels';
    }
    if (domain.includes('joinsecret')) {
      return 'Plateforme de découverte de solutions logicielles';
    }
    if (domain.includes('capterra')) {
      return 'Site de comparaison de logiciels professionnels';
    }
    if (domain.includes('toolify')) {
      return 'Annuaire d\'outils et alternatives logicielles';
    }
    if (domain.includes('similarweb')) {
      return 'Plateforme d\'analyse de trafic et concurrence web';
    }

    // Vérifier si le titre est une URL ou contient un domaine
    const isTitleUrl = title.includes('www.') || title.includes('.com') || title.includes('.org') || title.includes('.fr');

    // Pour les autres sites, utiliser le titre s'il est disponible et n'est pas une URL
    if (title && !isTitleUrl) {
      // Limiter la longueur de la description
      return title.length > 60 ? title.substring(0, 60) + '...' : title;
    }

    // Si le titre est une URL mais différent du domaine actuel, c'est peut-être un titre plus descriptif
    if (title && title !== domain && !title.includes(domain)) {
      return title.length > 60 ? title.substring(0, 60) + '...' : title;
    }

    // Fallback
    return `Site web ${friendlyName}`;
  };

  const domain = extractDomain(source.url);
  const friendlyName = extractFriendlyName(source.url);
  const description = generateDescription(source.url, source.title);

  return (
    <a
      href={source.url}
      target="_blank"
      rel="noopener noreferrer"
      className="source-tile block p-1.5 rounded-lg border border-tango-300 bg-tango-100/30 hover:bg-tango-200/40 hover:border-tango-400 transition-colors duration-150 h-full relative shadow-inner group"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <div className="flex items-center">
        <div className="source-favicon mr-1.5 flex-shrink-0">
          <img
            src={`https://www.google.com/s2/favicons?domain=${domain}&sz=32`}
            alt={domain}
            className="w-4 h-4"
            onError={(e) => {
              // Fallback si l'image ne charge pas
              (e.target as HTMLImageElement).src = 'data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgc3Ryb2tlPSJjdXJyZW50Q29sb3IiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIiBjbGFzcz0ibHVjaWRlIGx1Y2lkZS1nbG9iZSI+PGNpcmNsZSBjeD0iMTIiIGN5PSIxMiIgcj0iMTAiLz48cGF0aCBkPSJNMiAxMmgyMCIvPjxwYXRoIGQ9Ik0xMiAyYTkuOTQgOS45NCAwIDAgMSA3LjUgMy4yNSIvPjxwYXRoIGQ9Ik0xMiAyYTkuOTQgOS45NCAwIDAgMC03LjUgMy4yNSIvPjxwYXRoIGQ9Ik0xMiAyMmE5Ljk0IDkuOTQgMCAwIDAtNy41LTMuMjUiLz48cGF0aCBkPSJNMTIgMjJhOS45NCA5Ljk0IDAgMCAxIDcuNS0zLjI1Ii8+PHBhdGggZD0iTTEyIDJWMjIiLz48L3N2Zz4=';
            }}
          />
        </div>
        <div className="source-content flex-1 min-w-0">
          <div className="source-title text-xs font-medium text-black group-hover:text-tango-600 group-hover:underline line-clamp-1">
            {friendlyName.length > 25 ? friendlyName.substring(0, 25) + '...' : friendlyName}
          </div>
        </div>
      </div>

      {/* Tooltip de description qui apparaît au survol */}
      {isHovered && (
        <div className="absolute z-50 left-0 top-full mt-1.5 p-2.5 bg-gray-800 border border-gray-600 rounded-md shadow-lg text-xs text-white w-64 max-w-[300px]">
          <div className="absolute -top-2 left-3 w-3 h-3 bg-gray-800 border-t border-l border-gray-600 transform rotate-45"></div>
          {description}
        </div>
      )}
    </a>
  );
}
