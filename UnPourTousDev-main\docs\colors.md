# Système de couleurs de l'application UnPourTous

Ce document décrit le système de couleurs utilisé dans l'application UnPourTous, expliquant comment les couleurs sont organisées, où elles sont définies, et comment les utiliser correctement pour maintenir la cohérence visuelle.

## Architecture du système de couleurs

Le système de couleurs de l'application est organisé en plusieurs couches :

1. **Définition des couleurs dans Tailwind** : Les couleurs de base sont définies dans `tailwind.config.js`
2. **Variables CSS** : Les couleurs sont exposées comme variables CSS dans `:root` dans `index.css`
3. **Classes utilitaires Tailwind** : Les couleurs sont accessibles via les classes Tailwind (ex: `bg-primary`, `text-claude-orange`)
4. **Variables spécifiques aux composants** : Certains composants peuvent définir leurs propres variables de couleur

## Palettes de couleurs principales

### Palette principale (UnPourTous)

La palette principale est basée sur les couleurs du logo UnPourTous :

| Nom | Hex | Variable CSS | Classe Tailwind | Usage |
|-----|-----|--------------|-----------------|-------|
| **Bronze** | #4b321c | `--color-text-light` | `text-text-light` | Texte principal (mode clair) |
| **Tango** | #e37314 | `--color-primary` | `bg-primary` | Couleur d'accent principale, boutons |
| **Fire** | #dc4b04 | `--color-secondary` | `bg-secondary` | Actions importantes, alertes |

### Palette Claude (Interface Comfy)

Une palette secondaire est utilisée pour l'interface Claude/Comfy :

| Nom | Hex | Variable CSS | Classe Tailwind | Usage |
|-----|-----|--------------|-----------------|-------|
| Violet | #5D5CDE | `--color-claude-primary` | `bg-claude-primary` | Couleur principale de l'interface Claude |
| Orange | #e37313 | `--color-claude-orange` | `bg-claude-orange` | Accents, boutons dans l'interface Claude (harmonisé avec la couleur tango principale) |
| Gris foncé | #333333 | `--color-claude-gray` | `bg-claude-gray` | Arrière-plans secondaires |
| Gris clair | #444444 | `--color-claude-light-gray` | `bg-claude-light-gray` | Éléments d'interface, bordures |

## Couleurs fonctionnelles

### Couleurs de base du système

| Nom | Mode clair | Mode sombre | Variable CSS | Classe Tailwind |
|-----|------------|-------------|--------------|-----------------|
| Surface | #ffffff | #0f0e0a | `--color-surface-light`/`--color-surface-dark` | `bg-surface-light`/`bg-surface-dark` |
| Texte | #4b321c | #fdeed9 | `--color-text-light`/`--color-text-dark` | `text-text-light`/`text-text-dark` |
| Bordure | #e2d3c3 | #6d4e36 | `--color-border-light`/`--color-border-dark` | `border-border-light`/`border-border-dark` |

### Couleurs d'accent

| Nom | Valeur par défaut | Hover | Light | Dark | Variable CSS | Classe Tailwind |
|-----|-------------------|-------|-------|------|--------------|-----------------|
| Primary | #e37314 | #c76915 | #f68414 | #b46017 | `--color-primary` | `bg-primary` |
| Secondary | #dc4b04 | #b83e03 | #f47c14 | #943208 | `--color-secondary` | `bg-secondary` |

### Couleurs des catégories de modèles

| Catégorie | Couleur | Variable CSS | Classe Tailwind |
|-----------|---------|--------------|-----------------|
| Chat | #3b82f6 | `--color-model-chat` | `bg-blue-500` |
| Raisonnement | #8b5cf6 | `--color-model-reasoning` | `bg-purple-500` |
| Recherche Web | #10b981 | `--color-model-web-search` | `bg-green-500` |

## Comment utiliser les couleurs

### Bonnes pratiques

1. **Utiliser les classes Tailwind** : Préférez utiliser les classes Tailwind pour appliquer les couleurs (`bg-primary`, `text-text-light`, etc.)

   ```jsx
   <button className="bg-primary hover:bg-primary-hover text-white">
     Bouton primaire
   </button>
   ```

2. **Utiliser les variables CSS** pour les cas spéciaux :

   ```css
   .custom-element {
     background: linear-gradient(var(--color-primary), var(--color-primary-dark));
   }
   ```

3. **Respecter la sémantique des couleurs** :
   - `primary` : Actions principales, boutons d'action positive
   - `secondary` : Actions secondaires, alternatives
   - `claude-*` : Uniquement pour l'interface Claude/Comfy

4. **Support du mode sombre** : Utilisez les variantes dark de Tailwind pour le mode sombre :

   ```jsx
   <div className="bg-surface-light dark:bg-surface-dark text-text-light dark:text-text-dark">
     Contenu adaptatif
   </div>
   ```

### À éviter

1. **Ne pas coder en dur les valeurs hexadécimales** : Utilisez toujours les classes Tailwind ou les variables CSS
2. **Ne pas créer de nouvelles couleurs** sans les ajouter au système centralisé
3. **Ne pas mélanger les palettes** : Ne pas utiliser les couleurs Claude dans l'interface principale et vice-versa

## Où sont définies les couleurs

1. **tailwind.config.js** : Définition principale des couleurs pour Tailwind
2. **src/index.css** : Variables CSS globales
3. **src/styles/claudeInterface.css** : Styles spécifiques à l'interface Claude
4. **src/config/modelCategories.ts** : Couleurs des catégories de modèles

## Évolution du système de couleurs

Pour ajouter ou modifier des couleurs :

1. Ajouter la couleur dans `tailwind.config.js`
2. Ajouter la variable CSS correspondante dans `index.css`
3. Mettre à jour cette documentation
4. Communiquer le changement à l'équipe
