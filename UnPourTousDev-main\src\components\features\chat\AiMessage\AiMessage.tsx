import React from "react";
import { AiMessageProps } from "./types";
import { useAiMessage } from "./hooks/useAiMessage";
import MessageHeader from "./components/MessageHeader";
import MessageContent from "./components/MessageContent";

/**
 * Composant AiMessage principal
 */
const AiMessage: React.FC<AiMessageProps> = ({ message }) => {
  // Utiliser le hook personnalisé pour gérer l'état et la logique
  const {
    showCursor,
    showAllSources,
    setShowAllSources,
    modelName,
    isWebSearchModel,
    isAutoRouter
  } = useAiMessage(message);

  return (
    <div className="flex mb-4 w-full">
      <div className="bg-comfy-light-gray/70 rounded-xl p-4 max-w-[95%] text-white">
        {/* En-tête du message */}
        <MessageHeader
          modelName={modelName}
          modelUsed={message.modelUsed}
          isWebSearchModel={isWebSearchModel}
          isAutoRouter={isAutoRouter}
        />

        {/* Contenu du message */}
        <MessageContent
          content={message.content}
          references={message.references}
          isStreaming={message.isStreaming}
          showCursor={showCursor}
        />
      </div>
    </div>
  );
};

export default AiMessage;
