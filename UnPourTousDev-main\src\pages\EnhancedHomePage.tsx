import React, { useState, useRef } from "react";
import { useNavigate } from "react-router-dom";
import { Id } from "../../convex/_generated/dataModel";
import { useChat } from "../components/features/chat/ComfyInterface/hooks/useChat";
import { useModelSelection, AUTOSELECT_ID } from "../components/features/chat/ComfyInterface/hooks/useModelSelection";
import ChatArea from "../components/features/chat/ComfyInterface/components/ChatArea";
import MessageInput from "../components/features/chat/ComfyInterface/components/MessageInput";
import ModelSelector from "../components/features/chat/ComfyInterface/components/ModelSelector";
import logo from "../assets/logo3.svg";

/**
 * Page d'accueil améliorée adaptée pour fonctionner à l'intérieur du MainLayout
 * Contient uniquement le contenu principal, sans la sidebar
 */
const EnhancedHomePage: React.FC = () => {
  const navigate = useNavigate();

  // Fonction appelée lorsqu'une nouvelle conversation est créée
  const handleConversationCreated = (conversationId: Id<"conversations">) => {
    // Rediriger vers la page de conversation
    navigate(`/c/${conversationId}`);
  };

  // Utiliser les hooks personnalisés
  const {
    userMessage,
    setUserMessage,
    chatStarted,
    assistantResponse,
    isLoading,
    currentConversationId,
    activeButton,
    setActiveButton,
    bottomRef,
    conversations,
    handleSidebarClick,
    handleSelectConversation,
    handleNewConversation,
    handleKeyDown,
    startChat: originalStartChat
  } = useChat(handleConversationCreated);

  // Utiliser le hook pour la sélection des modèles
  const {
    selectedModel,
    setSelectedModel,
    useAutoRouter,
    setUseAutoRouter,
    openCategory,
    toggleCategory,
    models,
    activeSelection,
    handleAutoSelect,
    handleModelSelect
  } = useModelSelection();

  // Fonction pour démarrer le chat avec le modèle sélectionné
  const startChat = (modelIdToUse: string) => {
    console.log("[EnhancedHomePage.tsx] startChat appelé avec modelId:", modelIdToUse);

    // Vérifier si modelIdToUse est différent de AUTOSELECT_ID
    if (modelIdToUse !== AUTOSELECT_ID) {
      // Si un modèle spécifique est sélectionné, utiliser son ID
      console.log("[EnhancedHomePage.tsx] Modèle spécifique sélectionné:", modelIdToUse);
      originalStartChat(modelIdToUse);
    } else {
      // Si AutoSelect est sélectionné, passer explicitement AUTOSELECT_ID
      console.log("[EnhancedHomePage.tsx] AutoSelect sélectionné, utilisation de:", AUTOSELECT_ID);
      originalStartChat(AUTOSELECT_ID);
    }
  };

  // Rendu de l'écran de bienvenue
  const renderWelcomeScreen = () => (
    <div className="flex-1 flex flex-col items-center justify-center p-4">
      <div className="flex justify-center mb-6">
        <img src={logo} alt="UnPourTous Logo" className="w-24 h-24" />
      </div>
      <p className="text-gray-400 text-center text-lg font-body mb-4">Je suis là pour discuter, aider ou simplement écouter</p>

      {/* Zone de saisie centrée */}
      <div className="w-full max-w-3xl mx-auto mt-4">
        <MessageInput
          userMessage={userMessage}
          setUserMessage={setUserMessage}
          isLoading={isLoading}
          handleKeyDown={handleKeyDown}
          startChat={startChat}
          selectedModel={selectedModel}
          setSelectedModel={setSelectedModel}
          useAutoRouter={useAutoRouter}
          setUseAutoRouter={setUseAutoRouter}
        />
      </div>
    </div>
  );

  // Rendu de l'écran de chat
  const renderChatScreen = () => (
    <div className="flex-1 overflow-y-auto p-4 space-y-6">
      <div className="max-w-3xl mx-auto">
        {userMessage && (
          <div className="mb-6">
            <div className="bg-claude-light-gray/20 rounded-xl p-4">
              <div className="font-medium mb-1 text-gray-400 font-body">Vous</div>
              <div className="text-white font-body">{userMessage || "Comment puis-je vous aider ?"}</div>
            </div>
          </div>
        )}
        {(assistantResponse || isLoading) && (
          <div>
            <div className="bg-claude-light-gray/45 rounded-xl p-4">
              <div className="font-medium mb-1 text-gray-400 font-body">Assistant IA</div>
              {isLoading ? (
                <div className="text-white font-body">
                  <span className="animate-pulse">...</span>
                </div>
              ) : (
                <div className="text-white font-body">{assistantResponse}</div>
              )}
            </div>
          </div>
        )}
        <div ref={bottomRef} />
      </div>
    </div>
  );

  // Rendu du contenu principal uniquement, sans la sidebar
  // La sidebar est maintenant gérée par le MainLayout
  return (
    <div className="flex-1 flex flex-col overflow-hidden">
      {/* Chat Area */}
      <ChatArea
        chatStarted={chatStarted}
        conversationId={currentConversationId}
        userMessage={userMessage}
        assistantResponse={assistantResponse}
        isLoading={isLoading}
        renderWelcomeScreen={renderWelcomeScreen}
        renderChatScreen={renderChatScreen}
      />

      {/* Model Selector (visible uniquement si une catégorie est ouverte) */}
      {openCategory && (
        <div className="p-4 border-t border-claude-light-gray">
          <div className="max-w-3xl mx-auto">
            <ModelSelector
              models={models}
              selectedModel={activeSelection}
              setSelectedModel={handleModelSelect}
              openCategory={openCategory}
            />
          </div>
        </div>
      )}

      {/* Message Input (visible uniquement si l'écran de bienvenue n'est pas affiché) */}
      {chatStarted && (
        <div className="p-4 border-t border-claude-light-gray">
          <div className="max-w-3xl mx-auto">
            <MessageInput
              userMessage={userMessage}
              setUserMessage={setUserMessage}
              isLoading={isLoading}
              handleKeyDown={handleKeyDown}
              startChat={startChat}
              selectedModel={selectedModel}
              setSelectedModel={setSelectedModel}
              useAutoRouter={useAutoRouter}
              setUseAutoRouter={setUseAutoRouter}
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default EnhancedHomePage;
