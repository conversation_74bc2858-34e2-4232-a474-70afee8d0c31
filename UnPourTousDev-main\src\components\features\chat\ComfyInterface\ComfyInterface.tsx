import React, { useRef } from "react";
import { ComfyInterfaceProps } from "./types";
import { useChat } from "./hooks/useChat";
import { useModelSelection, AUTOSELECT_ID } from "./hooks/useModelSelection";
import Sidebar from "./components/Sidebar";
import ChatArea from "./components/ChatArea";
import MessageInput from "./components/MessageInput";
import logo from "../../../../assets/logo2.svg";

/**
 * Composant principal ComfyInterface
 */
const ComfyInterface: React.FC<ComfyInterfaceProps> = ({
  onConversationCreated
}) => {
  // Utiliser les hooks personnalisés pour la sélection des modèles
  const {
    selectedModel,
    setSelectedModel,
    useAutoRouter,
    setUseAutoRouter,
    openCategory,
    toggleCategory,
    models,
    activeSelection
  } = useModelSelection();

  // Utiliser les hooks personnalisés pour le chat
  const {
    userMessage,
    setUserMessage,
    chatStarted,
    assistantResponse,
    isLoading,
    currentConversationId,
    activeButton,
    bottomRef,
    conversations,
    handleSidebarClick,
    handleSelectConversation,
    handleNewConversation,
    handleKeyDown,
    startChat: originalStartChat
  } = useChat(onConversationCreated);

  // Fonction pour démarrer un chat avec le modèle sélectionné
  const startChat = (modelId?: string) => {
    // Utiliser le modèle fourni ou celui sélectionné
    const finalModelId = modelId || (activeSelection === AUTOSELECT_ID ? "openrouter/auto" : activeSelection);

    console.log("Démarrage du chat avec le modèle:", finalModelId);
    console.log("useAutoRouter:", activeSelection === AUTOSELECT_ID);

    // Appeler la fonction originale avec le modèle sélectionné
    originalStartChat(finalModelId);
  };

  // Rendu de l'écran de bienvenue
  const renderWelcomeScreen = () => (
    <div className="flex flex-col flex-1 justify-center items-center px-4">
      <div className="flex flex-col items-center w-full max-w-3xl">
        {/* État de chat vide */}
        <div className="flex flex-col items-center justify-center mb-8">
          <div className="flex justify-center mb-6">
            <img src={logo} alt="UnPourTous Logo" className="w-24 h-24" />
          </div>
          <p className="text-gray-400 text-center text-lg font-body mb-4">
            Je suis là pour discuter, aider ou simplement écouter
          </p>
        </div>
      </div>
    </div>
  );

  // Rendu de l'écran de chat
  const renderChatScreen = () => (
    <div className="flex-1 overflow-y-auto p-4 space-y-6">
      {userMessage && (
        <div className="max-w-4xl mx-auto">
          <div className="bg-comfy-light-gray/20 rounded-xl p-4">
            <div className="font-medium mb-1 text-gray-400 font-body">Vous</div>
            <div className="text-white font-body">{userMessage || "Comment puis-je vous aider ?"}</div>
          </div>
        </div>
      )}

      {assistantResponse && (
        <div className="max-w-4xl mx-auto">
          <div className="bg-comfy-light-gray/70 rounded-xl p-4">
            <div className="font-medium mb-1 text-gray-400 font-body">Assistant IA</div>
            {isLoading ? (
              <div className="flex items-center">
                <span className="loader"></span>
              </div>
            ) : (
              <div className="text-white font-body">{assistantResponse}</div>
            )}
          </div>
        </div>
      )}

      <div ref={bottomRef}></div>
    </div>
  );

  return (
    <div className="comfy-interface h-screen flex flex-col bg-claude-dark text-white">
      <div className="flex flex-1 overflow-hidden">
        {/* Sidebar */}
        <Sidebar
          activeButton={activeButton}
          handleSidebarClick={handleSidebarClick}
          conversations={conversations}
          currentConversationId={currentConversationId}
          onSelectConversation={handleSelectConversation}
          onNewConversation={handleNewConversation}
        />

        {/* Main Content */}
        <div className="flex-1 flex flex-col overflow-hidden">
          {/* Chat Area */}
          <ChatArea
            chatStarted={chatStarted}
            conversationId={currentConversationId}
            userMessage={userMessage}
            assistantResponse={assistantResponse}
            isLoading={isLoading}
            renderWelcomeScreen={renderWelcomeScreen}
            renderChatScreen={renderChatScreen}
          />

          {/* Message Input */}
          <MessageInput
            userMessage={userMessage}
            setUserMessage={setUserMessage}
            isLoading={isLoading}
            handleKeyDown={handleKeyDown}
            startChat={startChat}
            selectedModel={selectedModel}
            setSelectedModel={setSelectedModel}
            useAutoRouter={useAutoRouter}
            setUseAutoRouter={setUseAutoRouter}
          />
        </div>
      </div>
    </div>
  );
};

export default ComfyInterface;
