/* Styles pour le contenu Markdown */

/* Styles pour les tableaux */
.markdown-content table {
  width: 100%;
  border-collapse: collapse;
  margin: 1rem 0;
  overflow-x: auto;
  display: block;
}

.markdown-content table th,
.markdown-content table td {
  padding: 0.5rem;
  border: 1px solid #e2e8f0;
  text-align: left;
}

.dark .markdown-content table th,
.dark .markdown-content table td {
  border-color: #4a5568;
}

.markdown-content table th {
  background-color: #f7fafc;
  font-weight: 600;
}

.dark .markdown-content table th {
  background-color: #2d3748;
}

.markdown-content table tr:nth-child(even) {
  background-color: #f7fafc;
}

.dark .markdown-content table tr:nth-child(even) {
  background-color: #2d3748;
}

.markdown-content table tr:hover {
  background-color: #edf2f7;
}

.dark .markdown-content table tr:hover {
  background-color: #4a5568;
}

/* Styles pour les blocs de code */
.markdown-content pre {
  background-color: #2d3748;
  color: #e2e8f0;
  padding: 1rem;
  border-radius: 0.375rem;
  overflow-x: auto;
  margin: 1rem 0;
}

.markdown-content code {
  font-family: monospace;
}

/* Styles pour les liens */
.markdown-content a {
  color: #e37314;
  text-decoration: none;
}

.markdown-content a:hover {
  text-decoration: underline;
}

/* Styles pour les listes */
.markdown-content ul,
.markdown-content ol {
  padding-left: 1.5rem;
  margin: 1rem 0;
}

/* Styles pour les titres */
.markdown-content h1,
.markdown-content h2,
.markdown-content h3,
.markdown-content h4,
.markdown-content h5,
.markdown-content h6 {
  margin-top: 1.5rem;
  margin-bottom: 1rem;
  font-weight: 600;
  line-height: 1.25;
}

.markdown-content h1 {
  font-size: 1.875rem;
}

.markdown-content h2 {
  font-size: 1.5rem;
}

.markdown-content h3 {
  font-size: 1.25rem;
}

/* Styles pour les citations */
.markdown-content blockquote {
  border-left: 4px solid #e2e8f0;
  padding-left: 1rem;
  margin: 1rem 0;
  color: #718096;
}

.dark .markdown-content blockquote {
  border-left-color: #4a5568;
  color: #a0aec0;
}

/* Styles pour les images */
.markdown-content img {
  max-width: 100%;
  height: auto;
  margin: 1rem 0;
}

/* Styles pour les références */
.markdown-content .reference-link {
  color: #e37314;
  font-weight: 500;
}

.markdown-content .reference-link:hover {
  text-decoration: underline;
}
