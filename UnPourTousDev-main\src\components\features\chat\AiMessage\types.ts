/**
 * Interface pour une référence (source) dans un message IA
 */
export interface Reference {
  id: string;
  title: string;
  url: string;
}

/**
 * Interface pour les propriétés du composant AiMessage
 */
export interface AiMessageProps {
  message: {
    _id: string;
    content: string;
    modelUsed: string;
    modelName?: string;
    isStreaming?: boolean;
    references?: Reference[];
  };
}

/**
 * Interface pour les propriétés du composant MessageHeader
 */
export interface MessageHeaderProps {
  modelName: string;
  modelUsed: string;
  isWebSearchModel: boolean;
  isAutoRouter: boolean;
}

/**
 * Interface pour les propriétés du composant MessageContent
 */
export interface MessageContentProps {
  content: string;
  references?: Reference[];
  isStreaming?: boolean;
  showCursor: boolean;
}

/**
 * Interface pour les propriétés du composant SourcesList
 */
export interface SourcesListProps {
  references: Reference[];
  showAllSources: boolean;
  setShowAllSources: (show: boolean) => void;
}

/**
 * Interface pour les propriétés du composant MarkdownRenderer
 */
export interface MarkdownRendererProps {
  content: string;
  references?: Reference[];
  isStreaming?: boolean;
  showCursor: boolean;
}
