import defaultTheme from "tailwindcss/defaultTheme";

/** @type {import('tailwindcss').Config} */
export default {
  content: ["./index.html", "./src/**/*.{js,ts,jsx,tsx}"],
  darkMode: 'class', // Activer le mode sombre basé sur les classes
  theme: {
    extend: {
      fontFamily: {
        sans: ["Inter var", ...defaultTheme.fontFamily.sans],
      },
      colors: {
        // Couleurs de base du système
        surface: {
          light: '#ffffff', // Fond en mode clair
          dark: '#0f0e0a',  // Fond en mode sombre (rgba(15,14,10,255))
        },
        text: {
          light: '#4b321c', // Texte principal en mode clair (Bronze-700)
          dark: '#fdeed9',  // Texte principal en mode sombre (Tango-100)
        },

        // Couleurs d'accent
        primary: {
          DEFAULT: '#e37314', // Tango-500
          hover: '#c76915',   // Tango-600
          light: '#f68414',   // Tango-400
          dark: '#b46017',    // Tango-700
        },
        secondary: {
          DEFAULT: '#dc4b04', // Fire-600 (Trinidad)
          hover: '#b83e03',   // Fire-800
          light: '#f47c14',   // Fire-400 (Ecstasy)
          dark: '#943208',    // Fire-900
        },

        // Couleurs de bordure
        border: {
          light: '#e2d3c3', // Bronze-200
          dark: '#6d4e36',  // Bronze-600
        },

        // Palette bronze
        bronze: {
          50: "#f9f5f2",
          100: "#f0e9e2",
          200: "#e2d3c3",
          300: "#d2b9a1",
          400: "#b99878",
          500: "#8e4f1a", // rope
          600: "#6d4e36",
          700: "#4b321c", // metallic-bronze
          800: "#3a2616",
          900: "#2a1b10",
        },

        // Palette tango
        tango: {
          50: "#fef8f1",
          100: "#fdeed9",
          200: "#fad9b3",
          300: "#f8c082",
          400: "#f68414", // ecstasy
          500: "#e37314", // tango
          600: "#c76915", // orange-roughy
          700: "#b46017", // reno-sand
          800: "#943208",
          900: "#7a2a0a",
        },

        // Palette fire
        fire: {
          50: "#fff5f2",
          100: "#ffe9e2",
          200: "#ffd0c2",
          300: "#ffb199",
          400: "#f47c14", // ecstasy
          500: "#df5c06", // bamboo
          600: "#dc4b04", // trinidad
          700: "#db3b04", // grenadier
          800: "#b83e03",
          900: "#943208",
        },

        // Comfy interface colors (harmonized with tango orange)
        'comfy-primary': '#5D5CDE',
        'comfy-orange': '#e37313', // Tango orange for consistency
        'comfy-dark': '#1A1A1A',
        'comfy-darker': '#141414',
        'comfy-gray': '#333333',
        'comfy-light-gray': '#444444',

        // Backward compatibility - keep claude colors for now
        'claude-primary': '#5D5CDE',
        'claude-orange': '#FF8E6E',
        'claude-dark': '#1A1A1A',
        'claude-darker': '#141414',
        'claude-gray': '#333333',
        'claude-light-gray': '#444444',
      },
    },
  },
  plugins: [
    require('@tailwindcss/typography'),
  ],
};
