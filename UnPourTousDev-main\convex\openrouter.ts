import { action } from "./_generated/server";
import { v } from "convex/values";
import { internal } from "./_generated/api";
import { api } from "./_generated/api";
import {
  isModelWithWebSearch,
  isModelWithStructuredOutput,
  isModelWithReasoning,
  isModelWithStreaming,
  getModelMaxTokens
} from "./utils";

// Fonction pour extraire les références des annotations des modèles avec recherche web
export function extractWebReferenceFromAnnotations(annotations: any[]): { id: string; title: string; url: string }[] {
  const references: { id: string; title: string; url: string }[] = [];

  if (!annotations || !Array.isArray(annotations)) {
    return references;
  }

  // Parcourt les annotations
  annotations.forEach((annotation, index) => {
    // Format Sonar: { type: "url_citation", url_citation: { title: "...", url: "..." } }
    if (annotation.type === "url_citation" && annotation.url_citation) {
      references.push({
        id: (index + 1).toString(), // Utilise l'index + 1 comme ID
        title: annotation.url_citation.title || `Source ${index + 1}`,
        url: annotation.url_citation.url,
      });
    }
    // Format Claude: { type: "source", source: { title: "...", url: "..." } }
    else if (annotation.type === "source" && annotation.source) {
      references.push({
        id: (index + 1).toString(),
        title: annotation.source.title || `Source ${index + 1}`,
        url: annotation.source.url,
      });
    }
    // Format GPT: { index: 1, citation: { title: "...", url: "..." } }
    else if (annotation.citation) {
      references.push({
        id: (annotation.index || (index + 1)).toString(),
        title: annotation.citation.title || `Source ${annotation.index || (index + 1)}`,
        url: annotation.citation.url,
      });
    }
    // Format générique: { url: "...", title: "..." }
    else if (annotation.url) {
      references.push({
        id: (index + 1).toString(),
        title: annotation.title || annotation.text || `Source ${index + 1}`,
        url: annotation.url,
      });
    }
  });

  return references;
}

// Fonction pour formater le nom du modèle
export function formatModelName(modelId: string, customName?: string): string {
  // Si un nom personnalisé est fourni, l'utiliser
  if (customName) {
    return customName;
  }

  // Sinon, formater le nom du modèle à partir de son ID
  const parts = modelId.split('/');
  if (parts.length > 1) {
    // Formater le nom du modèle pour l'affichage (ex: "gpt-4-turbo" -> "Gpt 4 Turbo")
    return parts[1].replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  }

  // Si le format n'est pas standard, retourner l'ID tel quel
  return modelId;
}

// Fonction pour extraire les références d'un texte
export function extractReferences(text: string) {
  const references: { id: string; title: string; url: string }[] = [];
  const referencesMap = new Map<string, { id: string; title: string; url: string }>();
  let match;

  // Recherche les références au format [1]: titre (url)
  const formatOneRegex = /\[(\d+)\]:\s*(.*?)\s*\((https?:\/\/[^\s)]+)\)/g;
  while ((match = formatOneRegex.exec(text)) !== null) {
    referencesMap.set(match[1], {
      id: match[1],
      title: match[2].trim(),
      url: match[3].trim(),
    });
  }

  // Recherche les références au format [1] titre url
  const formatTwoRegex = /\[(\d+)\]\s*(.*?)\s*(https?:\/\/[^\s]+)/g;
  while ((match = formatTwoRegex.exec(text)) !== null) {
    if (!referencesMap.has(match[1])) {
      referencesMap.set(match[1], {
        id: match[1],
        title: match[2].trim(),
        url: match[3].trim(),
      });
    }
  }

  // Recherche les références au format [n] dans le texte et essaie de trouver l'URL correspondante
  const citationRegex = /\[(\d+)\]/g;
  const urlRegex = /(https?:\/\/[^\s]+)/g;
  const citations = new Set<string>();

  while ((match = citationRegex.exec(text)) !== null) {
    citations.add(match[1]);
  }

  // Si nous avons des citations mais pas de références, essayons de trouver des URLs
  if (citations.size > 0 && referencesMap.size === 0) {
    const urls: string[] = [];
    while ((match = urlRegex.exec(text)) !== null) {
      urls.push(match[1]);
    }

    // Associe chaque citation à une URL si possible
    let urlIndex = 0;
    citations.forEach(id => {
      if (urlIndex < urls.length) {
        referencesMap.set(id, {
          id,
          title: `Source ${id}`,
          url: urls[urlIndex++],
        });
      }
      // Ne pas créer de références fictives avec example.com
      // Si pas d'URL réelle, on ignore la citation
    });
  }

  // Convertit la Map en tableau
  referencesMap.forEach(ref => references.push(ref));

  // Trie les références par ID numérique
  references.sort((a, b) => parseInt(a.id) - parseInt(b.id));

  return references;
}

// Types pour l'API OpenRouter
export interface OpenRouterMessage {
  role: "user" | "assistant" | "system";
  content: string;
}

export interface OpenRouterRequest {
  model: string;
  messages: OpenRouterMessage[];
  max_tokens?: number;
  temperature?: number;
  top_p?: number;
  stream?: boolean;
  web_search_options?: {
    search_context_size: "low" | "medium" | "high";
  };
  plugins?: Array<{
    id: string;
    max_results?: number;
    search_prompt?: string;
  }>;
}

export interface OpenRouterResponse {
  id: string;
  choices: {
    message: {
      role: string;
      content: string;
      annotations?: any[];
      reasoning?: string;
      refusal?: string;
    };
    finish_reason: string;
    native_finish_reason?: string;
  }[];
  model: string;
  usage: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

// Types pour les modèles OpenRouter
export interface OpenRouterModel {
  id: string;
  name: string;
  created: number;
  description: string;
  context_length: number;
  pricing: {
    prompt: string;
    completion: string;
    image: string;
    request: string;
    web_search?: string;
    input_cache_read?: string;
    input_cache_write?: string;
    internal_reasoning?: string;
  };
  architecture?: {
    input_modalities?: string[];
    output_modalities?: string[];
    tokenizer?: string;
  };
  top_provider?: {
    is_moderated?: boolean;
  };
  supported_parameters?: string[];
  per_request_limits?: Record<string, any>;
}

// Fonction pour appeler l'API OpenRouter
export const callOpenRouter = action({
  args: {
    conversationId: v.id("conversations"),
    modelId: v.string(),
    modelName: v.optional(v.string()),
    messages: v.array(
      v.object({
        role: v.union(v.literal("user"), v.literal("assistant"), v.literal("system")),
        content: v.string(),
      })
    ),
  },
  handler: async (ctx, args) => {
    try {
      // Si le modèle est AutoRouter, on applique notre logique de routage personnalisée
      if (args.modelId === "openrouter/auto") {
        // Récupère le dernier message utilisateur
        const userMessages = args.messages.filter(m => m.role === "user");
        const userMessage = userMessages.length > 0 ? userMessages[userMessages.length - 1].content : "";

        // Récupère tous les modèles disponibles depuis la table livemodels
        const liveModels = await ctx.runQuery(api.livemodels.list);

        // Prépare les modèles pour NotDiamond ou Semantic-router
        const availableModels = liveModels.map(model => ({
          modelId: model.modelId,
          name: model.name,
          provider: model.provider || model.modelId.split('/')[0],
          webSearch: model.webSearch,
          reasoning: model.reasoning
        }));

        console.log("Modèles disponibles pour le routage:", availableModels.map(m => m.modelId));

        // Détermine le meilleur modèle à utiliser
        let bestModel = { modelId: "openrouter/auto", modelName: "AutoRouter" };

        try {
          // Essaie d'abord avec NotDiamond.AI
          const notDiamondApiKey = process.env.NOTDIAMOND_API_KEY;
          if (notDiamondApiKey) {
            console.log("Utilisation de NotDiamond.AI pour déterminer le meilleur modèle");
            bestModel = await ctx.runAction(api.notdiamond.getBestModel, {
              message: userMessage,
              availableModels: availableModels
            });
            console.log("NotDiamond.AI recommande:", bestModel);
          } else {
            // Si NotDiamond.AI n'est pas configuré, utilise Semantic-router
            const aurelioApiKey = process.env.AURELIO_API_KEY;
            if (aurelioApiKey) {
              console.log("NotDiamond.AI non configuré, utilisation de Semantic-router");
              bestModel = await ctx.runAction(api.semanticrouter.needsWebSearch, {
                message: userMessage,
                availableModels: availableModels
              });
              console.log("Semantic-router recommande:", bestModel);
            }
          }
        } catch (error) {
          console.error("Erreur lors de la détermination du meilleur modèle:", error);
          // En cas d'erreur, essaie avec Semantic-router comme fallback
          try {
            const aurelioApiKey = process.env.AURELIO_API_KEY;
            if (aurelioApiKey) {
              console.log("Erreur avec NotDiamond.AI, utilisation du fallback Semantic-router");
              bestModel = await ctx.runAction(api.semanticrouter.needsWebSearch, {
                message: userMessage,
                availableModels: availableModels
              });
              console.log("Semantic-router (fallback) recommande:", bestModel);
            }
          } catch (fallbackError) {
            console.error("Erreur lors de l'utilisation du fallback Semantic-router:", fallbackError);
            // En cas d'échec complet, on utilise l'AutoRouter standard
            console.log("Utilisation de l'AutoRouter standard suite aux erreurs");
          }
        }

        // Si un modèle a été recommandé, on l'utilise
        if (bestModel && bestModel.modelId !== "openrouter/auto") {
          console.log("Modèle sélectionné par le routage intelligent:", bestModel.modelId, `(${bestModel.modelName})`);

          // Vérifier les capacités du modèle sélectionné
          const selectedModel = liveModels.find(m => m.modelId === bestModel.modelId);
          const supportsStreaming = selectedModel?.streaming === true;
          const isWebSearchModel = selectedModel?.webSearch === true;

          if (supportsStreaming) {
            console.log("🚀 [AutoRouter] Le modèle sélectionné supporte le streaming, utilisation du streaming");
            // Utiliser le streaming pour le modèle sélectionné
            return await ctx.runAction(api.streaming.streamMessage, {
              conversationId: args.conversationId,
              modelId: bestModel.modelId,
              modelName: bestModel.modelName,
              messages: args.messages,
            });
          } else {
            console.log("📝 [AutoRouter] Le modèle sélectionné ne supporte pas le streaming, utilisation de l'API classique");
            // Remplace le modelId par celui recommandé pour l'API classique
            args.modelId = bestModel.modelId;
            args.modelName = bestModel.modelName;
          }
        }
      }

      // Prépare la requête pour OpenRouter
      const openRouterUrl = "https://openrouter.ai/api/v1/chat/completions";

      // Détermine le nombre maximum de tokens en fonction du modèle
      const maxTokens = getModelMaxTokens(args.modelId);

      console.log("OpenRouter Request:", {
        model: args.modelId,
        messagesCount: args.messages.length,
        messages: args.messages,
        max_tokens: maxTokens
      });

      // Appelle l'API OpenRouter
      const response = await fetch(openRouterUrl, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${process.env.OPENROUTER_API_KEY}`,
          "HTTP-Referer": "https://unpourtouschat.com",
          "X-Title": "UnPourTous Chat"
        },
        body: JSON.stringify({
          model: args.modelId,
          messages: args.messages,
          max_tokens: maxTokens,
        }),
      });

      console.log("OpenRouter Response Status:", response.status, response.statusText);

      if (!response.ok) {
        const errorText = await response.text();
        console.error("OpenRouter Error:", errorText);
        throw new Error(`OpenRouter Error: ${response.status} ${response.statusText}`);
      }

      // Traitement standard pour les requêtes non-streaming
      const data: OpenRouterResponse = await response.json();

      // Log pour le débogage
      console.log("OpenRouter Response Data:", {
        id: data.id,
        model: data.model,
        usage: data.usage,
        choices: data.choices,
      });

      // Extrait le contenu de la réponse
      const content = data.choices[0]?.message?.content || "Désolé, je n'ai pas pu générer de réponse.";
      console.log("OpenRouter Response Content:", content);

      // Vérifie si le modèle a des capacités de recherche web ou de raisonnement
      let references: { id: string; title: string; url: string }[] = [];
      const isWebSearchModel = isModelWithWebSearch(args.modelId);
      const isReasoningModel = isModelWithReasoning(args.modelId);

      // Traite les annotations si présentes (pour les modèles avec recherche web)
      if (data.choices[0]?.message?.annotations) {
        const annotations = data.choices[0].message.annotations;
        console.log("📋 [OpenRouter] Annotations trouvées:", JSON.stringify(annotations));

        // Extrait les références des annotations
        const extractedReferences = extractWebReferenceFromAnnotations(annotations);
        console.log("🔗 [OpenRouter] Références extraites des annotations:", JSON.stringify(extractedReferences));

        if (extractedReferences.length > 0) {
          references = extractedReferences;
        }
      }

      // Si pas de références des annotations, essayer d'extraire du texte
      if (references.length === 0 && content) {
        const textReferences = extractReferences(content);
        console.log("🔗 [OpenRouter] Références extraites du texte:", JSON.stringify(textReferences));

        if (textReferences.length > 0) {
          references = textReferences;
        }
      }

      console.log("📊 [OpenRouter] Références finales:", JSON.stringify(references));

      // Sauvegarde la réponse dans la base de données
      await ctx.runMutation(internal.messages.saveAssistantMessage, {
        conversationId: args.conversationId,
        content: content,
        modelId: args.modelId,
        modelName: args.modelName || data.model,
        references: references.length > 0 ? references : undefined,
      });

      return {
        success: true,
        usage: data.usage,
        model: data.model,
      };
    } catch (error) {
      console.error("Erreur lors de l'appel à OpenRouter:", error);

      // En cas d'erreur, sauvegarde un message d'erreur
      await ctx.runMutation(internal.messages.saveAssistantMessage, {
        conversationId: args.conversationId,
        content: `Désolé, une erreur s'est produite lors de la génération de la réponse. Veuillez réessayer.\n\nErreur: ${error instanceof Error ? error.message : String(error)}`,
        modelId: args.modelId,
        modelName: args.modelName,
      });

      return { success: false };
    }
  },
});

// Fonction pour récupérer les modèles disponibles via l'API OpenRouter
export const fetchAvailableModels = action({
  args: {},
  handler: async (_ctx) => {
    try {
      // Récupère la clé API depuis les variables d'environnement
      const apiKey = process.env.OPENROUTER_API_KEY;
      if (!apiKey) {
        throw new Error("La clé API OpenRouter n'est pas configurée");
      }

      // Appelle l'API OpenRouter pour récupérer les modèles disponibles
      const response = await fetch("https://openrouter.ai/api/v1/models", {
        method: "GET",
        headers: {
          "Authorization": `Bearer ${apiKey}`,
          "Content-Type": "application/json",
        },
      });

      // Vérifie si la requête a réussi
      if (!response.ok) {
        const errorData = await response.text();
        console.error("Erreur OpenRouter:", errorData);
        throw new Error(`Erreur OpenRouter: ${response.status} ${response.statusText}`);
      }

      // Parse la réponse
      const data = await response.json();

      // Retourne les modèles
      return data.data as OpenRouterModel[];
    } catch (error) {
      console.error("Erreur lors de la récupération des modèles OpenRouter:", error);
      throw error;
    }
  },
});

// Fonction pour synchroniser les modèles OpenRouter avec la base de données
export const syncModels = action({
  args: {},
  handler: async (ctx): Promise<{ success: boolean; added: number; updated: number }> => {
    try {
      // Récupère les modèles disponibles via l'API OpenRouter
      const apiKey = process.env.OPENROUTER_API_KEY;
      if (!apiKey) {
        throw new Error("La clé API OpenRouter n'est pas configurée");
      }

      // Appelle l'API OpenRouter pour récupérer les modèles disponibles
      const response = await fetch("https://openrouter.ai/api/v1/models", {
        method: "GET",
        headers: {
          "Authorization": `Bearer ${apiKey}`,
          "Content-Type": "application/json",
        },
      });

      // Vérifie si la requête a réussi
      if (!response.ok) {
        const errorData = await response.text();
        console.error("Erreur OpenRouter:", errorData);
        throw new Error(`Erreur OpenRouter: ${response.status} ${response.statusText}`);
      }

      // Parse la réponse
      const data = await response.json();
      const openRouterModels = data.data as OpenRouterModel[];

      // Convertir les modèles OpenRouter en format pour notre base de données
      const modelsToSync = [];

      // Traite chaque modèle
      for (const orModel of openRouterModels) {
        // Ignore les modèles qui ne sont pas de type texte
        if (orModel.architecture?.output_modalities &&
            !orModel.architecture.output_modalities.includes("text")) {
          continue;
        }

        // Détermine les capacités du modèle

        // Vérifie si le modèle a des capacités de recherche web
        const hasWebSearchPricing = orModel.pricing.web_search && parseFloat(orModel.pricing.web_search) > 0;
        const supportsWebSearchOptions = orModel.supported_parameters?.includes("web_search_options");
        const supportsPlugins = orModel.supported_parameters?.includes("plugins");
        const hasOnlineSuffix = orModel.id.endsWith(":online");
        const isWebSearchModel = hasWebSearchPricing || supportsWebSearchOptions || supportsPlugins ||
                                hasOnlineSuffix || isModelWithWebSearch(orModel.id);

        // Vérifie si le modèle supporte les sorties structurées
        const hasStructuredOutputSupport = orModel.supported_parameters?.includes("response_format") ||
                                          orModel.supported_parameters?.includes("structured_outputs") ||
                                          orModel.supported_parameters?.includes("json_object");
        const isStructuredOutputModel = hasStructuredOutputSupport || isModelWithStructuredOutput(orModel.id);

        // Vérifie si le modèle a des capacités de raisonnement
        const hasReasoningSupport = orModel.supported_parameters?.includes("reasoning") ||
                                   (orModel.pricing && orModel.pricing.internal_reasoning &&
                                    parseFloat(orModel.pricing.internal_reasoning) > 0);
        const isReasoningModel = hasReasoningSupport || isModelWithReasoning(orModel.id);

        // Vérifie si le modèle supporte le streaming
        const hasStreamingSupport = orModel.supported_parameters?.includes("stream");
        const isStreamingModel = hasStreamingSupport || isModelWithStreaming(orModel.id);

        console.log(`Modèle ${orModel.id}: webSearch=${isWebSearchModel}, structuredOutput=${isStructuredOutputModel}, reasoning=${isReasoningModel}, streaming=${isStreamingModel}`);

        // Détermine si le modèle est principalement pour le chat
        const isChatModel = !isWebSearchModel && !isReasoningModel;

        // Prépare le modèle à synchroniser
        modelsToSync.push({
          name: orModel.name,
          modelId: orModel.id,
          provider: orModel.id.split('/')[0] || "unknown",
          description: orModel.description || "",
          enabled: false, // Par défaut désactivé pour les nouveaux modèles
          contextLength: orModel.context_length || 4096,
          pricing: {
            prompt: parseFloat(orModel.pricing.prompt) || 0,
            completion: parseFloat(orModel.pricing.completion) || 0,
          },
          webSearch: isWebSearchModel,
          structuredOutput: isStructuredOutputModel,
          reasoning: isReasoningModel,
          streaming: isStreamingModel,
          chat: isChatModel,
        });
      }

      // Utilise la mutation syncModelsFromOpenRouter pour mettre à jour la base de données
      const result = await ctx.runMutation(api.models.syncModelsFromOpenRouter, {
        models: modelsToSync
      });

      return { success: true, added: result.added, updated: result.updated };
    } catch (error) {
      console.error("Erreur lors de la synchronisation des modèles:", error);
      throw error;
    }
  },
});

// Fonction pour récupérer les détails d'un modèle spécifique
export const fetchModelDetails = action({
  args: {
    modelId: v.string(),
  },
  handler: async (_ctx, args) => {
    try {
      // Récupère la clé API depuis les variables d'environnement
      const apiKey = process.env.OPENROUTER_API_KEY;
      if (!apiKey) {
        throw new Error("La clé API OpenRouter n'est pas configurée");
      }

      // Parse le modelId pour extraire l'auteur et le slug
      const [author, slug] = args.modelId.split('/');

      if (!author || !slug) {
        throw new Error("Format de modelId invalide. Format attendu: 'author/slug'");
      }

      // Appelle l'API OpenRouter pour récupérer les détails du modèle
      const response = await fetch(`https://openrouter.ai/api/v1/models/${author}/${slug}/endpoints`, {
        method: "GET",
        headers: {
          "Authorization": `Bearer ${apiKey}`,
          "Content-Type": "application/json",
        },
      });

      // Vérifie si la requête a réussi
      if (!response.ok) {
        const errorData = await response.text();
        console.error("Erreur OpenRouter:", errorData);
        throw new Error(`Erreur OpenRouter: ${response.status} ${response.statusText}`);
      }

      // Parse la réponse
      const data = await response.json();

      // Retourne les détails du modèle
      return data.data;
    } catch (error) {
      console.error("Erreur lors de la récupération des détails du modèle:", error);
      throw error;
    }
  },
});












