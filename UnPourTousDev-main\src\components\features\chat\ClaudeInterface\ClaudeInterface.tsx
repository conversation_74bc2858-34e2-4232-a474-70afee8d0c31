import React from "react";
import { ClaudeInterfaceProps } from "./types";
import { useChat } from "./hooks/useChat";
import { useModelSelection, AUTOSELECT_ID } from "./hooks/useModelSelection";
import Sidebar from "./components/Sidebar";
import ChatArea from "./components/ChatArea";
import MessageInput from "./components/MessageInput";
import logo from "../../../../assets/logo2.svg";
// import logo1 from "../../../../assets/LogoNewConversation2.png";

/**
 * Composant principal ClaudeInterface
 */
const ClaudeInterface: React.FC<ClaudeInterfaceProps> = ({
  onConversationCreated
}) => {
  // Utiliser les hooks personnalisés pour la sélection des modèles
  const {
    selectedModel,
    setSelectedModel,
    useAutoRouter,
    setUseAutoRouter,
    openCategory,
    toggleCategory,
    models,
    activeSelection,
    handleAutoSelect,
    handleModelSelect
  } = useModelSelection();

  // Utiliser les hooks personnalisés pour le chat
  const {
    userMessage,
    setUserMessage,
    chatStarted,
    assistantResponse,
    isLoading,
    currentConversationId,
    activeButton,
    bottomRef,
    conversations,
    handleSidebarClick,
    handleSelectConversation,
    handleNewConversation,
    handleKeyDown,
    startChat: originalStartChat
  } = useChat(onConversationCreated);

  // Fonction pour démarrer le chat avec le modèle sélectionné
  const startChat = (modelIdFromInput?: string) => {
    // Log de diagnostic pour voir la valeur de activeSelection et modelIdFromInput
    console.log("[ClaudeInterface.tsx] activeSelection dans ClaudeInterface:", activeSelection);
    console.log("[ClaudeInterface.tsx] modelIdFromInput reçu de MessageInput:", modelIdFromInput);
    console.log("[ClaudeInterface.tsx] AUTOSELECT_ID dans ClaudeInterface:", AUTOSELECT_ID);

    // Utiliser modelIdFromInput s'il est fourni, sinon utiliser activeSelection
    const modelIdToUse = modelIdFromInput || activeSelection;
    console.log("[ClaudeInterface.tsx] modelIdToUse final:", modelIdToUse);

    // Vérifier si modelIdToUse est différent de AUTOSELECT_ID
    if (modelIdToUse !== AUTOSELECT_ID) {
      // Si un modèle spécifique est sélectionné, utiliser son ID
      console.log("[ClaudeInterface.tsx] Modèle spécifique sélectionné:", modelIdToUse);
      originalStartChat(modelIdToUse);
    } else {
      // Si AutoSelect est sélectionné, passer explicitement AUTOSELECT_ID
      console.log("[ClaudeInterface.tsx] AutoSelect sélectionné, utilisation de:", AUTOSELECT_ID);
      originalStartChat(AUTOSELECT_ID);
    }
  };



  // Rendu de l'écran de bienvenue
  const renderWelcomeScreen = () => (
    <div className="flex-1 flex flex-col items-center justify-center p-4">
      <div className="flex justify-center mb-6">
        <img src={logo} alt="UnPourTous Logo" className="w-24 h-24" />
      </div>
      <p className="text-gray-400 text-center text-lg font-body mb-4">Je suis là pour discuter, aider ou simplement écouter</p>
    </div>
  );

  // Rendu de l'écran de chat
  const renderChatScreen = () => (
    <div className="flex-1 overflow-y-auto p-4 space-y-6">
      {userMessage && (
        <div className="max-w-4xl mx-auto">
          <div className="bg-claude-light-gray/20 rounded-xl p-4">
            <div className="font-medium mb-1 text-gray-400 font-body">Vous</div>
            <div className="text-white font-body">{userMessage || "Comment puis-je vous aider ?"}</div>
          </div>
        </div>
      )}
      {(assistantResponse || isLoading) && (
        <div className="max-w-4xl mx-auto">
          <div className="bg-claude-light-gray/45 rounded-xl p-4">
            <div className="font-medium mb-1 text-gray-400 font-body">Assistant IA</div>
            {isLoading ? (
              <div className="text-white font-body">
                <span className="animate-pulse">...</span>
              </div>
            ) : (
              <div className="text-white font-body">{assistantResponse}</div>
            )}
          </div>
        </div>
      )}
      <div ref={bottomRef} />
    </div>
  );

  return (
    <div className="claude-interface h-screen flex flex-col bg-claude-dark text-white">
      <div className="flex flex-1 overflow-hidden">
        {/* Sidebar */}
        <Sidebar
          activeButton={activeButton}
          handleSidebarClick={handleSidebarClick}
          conversations={conversations}
          currentConversationId={currentConversationId}
          onSelectConversation={handleSelectConversation}
          onNewConversation={handleNewConversation}
        />

        {/* Main Content */}
        <div className="flex-1 flex flex-col overflow-hidden">
          {/* Chat Area */}
          <ChatArea
            chatStarted={chatStarted}
            conversationId={currentConversationId}
            userMessage={userMessage}
            assistantResponse={assistantResponse}
            isLoading={isLoading}
            renderWelcomeScreen={renderWelcomeScreen}
            renderChatScreen={renderChatScreen}
          />

          {/* Le ModelSelector est maintenant affiché dans le composant MessageInput */}

          {/* Message Input */}
          <MessageInput
            userMessage={userMessage}
            setUserMessage={setUserMessage}
            isLoading={isLoading}
            handleKeyDown={handleKeyDown}
            startChat={startChat}
            selectedModel={selectedModel}
            setSelectedModel={setSelectedModel}
            useAutoRouter={useAutoRouter}
            setUseAutoRouter={setUseAutoRouter}
            models={models}
            activeSelection={activeSelection}
            openCategory={openCategory}
            toggleCategory={toggleCategory}
            handleAutoSelect={handleAutoSelect}
            handleModelSelect={handleModelSelect}
          />
        </div>
      </div>
    </div>
  );
};

export default ClaudeInterface;
