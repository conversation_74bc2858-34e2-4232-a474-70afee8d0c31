import React from "react";
import { MarkdownRendererProps } from "../types";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";

/**
 * Composant pour le rendu du contenu Markdown
 */
const MarkdownRenderer: React.FC<MarkdownRendererProps> = ({
  content,
  references,
  isStreaming,
  showCursor
}) => {
  // Fonction pour traiter les références dans le contenu
  const processReferences = (content: string): string => {
    if (!references || references.length === 0) {
      return content;
    }

    // Remplacer les références [n] par des liens Markdown
    let processedContent = content;
    references.forEach((ref, index) => {
      const refNumber = index + 1;
      const refRegex = new RegExp(`\\[${refNumber}\\]`, 'g');
      processedContent = processedContent.replace(
        refRegex,
        `[${refNumber}](${ref.url} "${ref.title}")`
      );
    });

    return processedContent;
  };

  // Traiter les références dans le contenu
  const processedContent = processReferences(content);

  return (
    <div className="relative">
      <div className="markdown-content prose dark:prose-invert prose-sm max-w-none break-words whitespace-pre-wrap">
        <ReactMarkdown
          remarkPlugins={[remarkGfm]} // Support pour les tableaux, listes de tâches, etc.
          components={{
            // Personnaliser le rendu des éléments pour éviter les barres de défilement
            pre: ({ children }) => (
              <div className="whitespace-pre-wrap break-words">{children}</div>
            ),
            code: ({ className, children, inline }) => {
              const match = /language-(\w+)/.exec(className || '');
              return !inline && match ? (
                // Blocs de code avec coloration syntaxique
                <div className="bg-gray-800 p-2 rounded whitespace-pre-wrap break-words my-2">
                  <code className={className}>
                    {children}
                  </code>
                </div>
              ) : (
                // Code en ligne
                <code className="bg-gray-700 px-1 py-0.5 rounded text-sm">
                  {children}
                </code>
              );
            },
            // Personnaliser le rendu des tableaux
            table: ({ children }) => (
              <div className="overflow-x-auto my-4">
                <table className="min-w-full divide-y divide-gray-300 dark:divide-gray-700 border border-gray-300 dark:border-gray-600">{children}</table>
              </div>
            ),
            th: ({ children }) => (
              <th className="px-4 py-2 bg-gray-100 dark:bg-gray-800 text-left text-xs font-medium text-gray-700 dark:text-gray-300 uppercase tracking-wider border border-gray-300 dark:border-gray-600">{children}</th>
            ),
            td: ({ children }) => (
              <td className="px-4 py-2 text-sm text-gray-900 dark:text-gray-200 border border-gray-300 dark:border-gray-600">{children}</td>
            ),
          }}
        >
          {processedContent}
        </ReactMarkdown>
      </div>

      {/* Curseur clignotant */}
      {isStreaming && showCursor && (
        <span className="animate-pulse absolute bottom-0 right-0">▌</span>
      )}
    </div>
  );
};

export default MarkdownRenderer;
