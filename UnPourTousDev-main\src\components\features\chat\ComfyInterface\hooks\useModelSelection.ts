import { useState, useCallback, useEffect } from "react";
import { useQuery } from "convex/react";
import { api } from "../../../../../../convex/_generated/api";
import { AIModel } from "../../ClaudeInterface/types";

// Constante pour l'ID d'AutoSelect
export const AUTOSELECT_ID = "autoselect";

/**
 * Hook personnalisé pour gérer la sélection des modèles
 * Gère la sélection des modèles, l'état useAutoRouter et les catégories
 */
export const useModelSelection = () => {
  // État principal pour la sélection active (modèle ou AutoSelect)
  const [activeSelection, setActiveSelection] = useState<string>(AUTOSELECT_ID);

  // États existants pour la compatibilité avec le reste de l'application
  const [selectedModel, setSelectedModel] = useState(AUTOSELECT_ID);
  const [useAutoRouter, setUseAutoRouter] = useState(true);

  // État pour la catégorie ouverte
  const [openCategory, setOpenCategory] = useState<string | null>(null);

  // Récupérer la liste des modèles depuis Convex
  const models = useQuery(api.livemodels.list) || [];

  // DEBUG: Tracer le flux de données
  console.log("🔍 [ComfyInterface/useModelSelection] ÉTAPE 3 - Hook useQuery");
  console.log("📊 Modèles bruts depuis Convex:", models);
  console.log("📊 Nombre de modèles:", models.length);
  if (models.length > 0) {
    console.log("📊 Premier modèle structure:", Object.keys(models[0]));
    console.log("📊 Premier modèle données:", models[0]);
  }

  // Fonction pour basculer l'affichage d'une catégorie
  const toggleCategory = (category: string) => {
    setOpenCategory(openCategory === category ? null : category);
  };

  // Fonction pour gérer la sélection d'AutoSelect
  const handleAutoSelect = () => {
    setActiveSelection(AUTOSELECT_ID);
    setSelectedModel(AUTOSELECT_ID);
    setUseAutoRouter(true);
    setOpenCategory(null); // Fermer les catégories
  };

  // Fonction pour gérer la sélection d'un modèle spécifique
  const handleModelSelect = (modelId: string) => {
    setActiveSelection(modelId);
    setSelectedModel(modelId);
    setUseAutoRouter(false);
    setOpenCategory(null); // Fermer les catégories
  };

  // Fonction pour synchroniser les états lors de changements externes
  const syncSelectedModel = (modelId: string) => {
    if (modelId === AUTOSELECT_ID || modelId === "openrouter/auto") {
      handleAutoSelect();
    } else {
      handleModelSelect(modelId);
    }
  };

  // Fonction pour synchroniser l'état useAutoRouter lors de changements externes
  const syncUseAutoRouter = (use: boolean) => {
    if (use) {
      handleAutoSelect();
    } else if (activeSelection === AUTOSELECT_ID) {
      // Si on désactive AutoRouter mais qu'aucun modèle n'est sélectionné,
      // sélectionner le premier modèle disponible
      const firstModel = models.find(m => m.enabled);
      if (firstModel) {
        handleModelSelect(firstModel.modelId);
      }
    }
  };

  return {
    // États principaux
    activeSelection,
    setActiveSelection,

    // États de compatibilité
    selectedModel,
    setSelectedModel: syncSelectedModel,
    useAutoRouter,
    setUseAutoRouter: syncUseAutoRouter,

    // Gestion des catégories
    openCategory,
    setOpenCategory,
    toggleCategory,

    // Données
    models,

    // Fonctions d'action
    handleAutoSelect,
    handleModelSelect
  };
};
