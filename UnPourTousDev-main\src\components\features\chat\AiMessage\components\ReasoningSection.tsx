import React, { useState } from 'react';
import { ChevronDownIcon, ChevronRightIcon } from '@heroicons/react/24/outline';
import MarkdownRenderer from '../../../../common/MarkdownRenderer';

interface ReasoningSectionProps {
  reasoningContent: string;
  isStreaming?: boolean;
  isReasoningModel?: boolean;
}

/**
 * Composant pour afficher la section de raisonnement collapsible
 */
const ReasoningSection: React.FC<ReasoningSectionProps> = ({
  reasoningContent,
  isStreaming = false,
  isReasoningModel = false
}) => {
  // Ouvert par défaut si on est en train de raisonner (streaming + modèle de raisonnement)
  const [isExpanded, setIsExpanded] = useState(isStreaming && isReasoningModel);

  if (!reasoningContent && !isStreaming) {
    return null;
  }

  return (
    <div className="mt-3 border-t border-claude-light-gray/30 pt-3">
      <button
        onClick={() => setIsExpanded(!isExpanded)}
        className="flex items-center gap-2 text-sm text-gray-400 hover:text-gray-300 transition-colors duration-200 mb-2"
      >
        {isExpanded ? (
          <ChevronDownIcon className="w-4 h-4" />
        ) : (
          <ChevronRightIcon className="w-4 h-4" />
        )}
        <span className="font-medium">
          {isStreaming && !reasoningContent ?
            "Raisonnement en cours..." :
            "Voir le raisonnement"
          }
        </span>
        {isStreaming && (
          <div className="flex items-center gap-1">
            <div className="w-1 h-1 bg-blue-400 rounded-full animate-pulse"></div>
            <div className="w-1 h-1 bg-blue-400 rounded-full animate-pulse delay-75"></div>
            <div className="w-1 h-1 bg-blue-400 rounded-full animate-pulse delay-150"></div>
          </div>
        )}
      </button>

      {isExpanded && (
        <div className="bg-claude-light-gray/20 rounded-lg p-3 border border-claude-light-gray/20">
          <div className="text-xs text-gray-500 mb-2 font-medium">
            💭 Processus de réflexion du modèle
          </div>

          {reasoningContent ? (
            <div className="text-sm text-gray-300">
              <MarkdownRenderer content={reasoningContent} />
              {isStreaming && (
                <span className="inline-block w-2 h-4 bg-gray-400 animate-pulse ml-1"></span>
              )}
            </div>
          ) : isStreaming ? (
            <div className="text-sm text-gray-400 italic">
              Le modèle réfléchit...
              <span className="inline-block w-2 h-4 bg-gray-400 animate-pulse ml-1"></span>
            </div>
          ) : (
            <div className="text-sm text-gray-500 italic">
              Aucun raisonnement disponible pour ce message.
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default ReasoningSection;
