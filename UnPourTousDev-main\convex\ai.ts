import { action, query } from "./_generated/server";
import { v } from "convex/values";
import { internal, api } from "./_generated/api";
import { Doc } from "./_generated/dataModel";

// Types pour les messages
interface Message {
  role: "user" | "assistant" | "system";
  content: string;
}

// Fonction pour obtenir une réponse de l'IA
export const getResponse = action({
  args: {
    conversationId: v.id("conversations"),
    modelId: v.string(),
    modelName: v.optional(v.string()),
  },
  handler: async (ctx, args): Promise<{ success: boolean; simulated?: boolean; usage?: any; model?: string }> => {
    try {
      // Récupère la conversation pour vérifier si elle utilise AutoRouter
      const conversation = await ctx.runQuery(api.conversations.getById, {
        conversationId: args.conversationId
      });
      
      // Si la conversation n'utilise pas AutoRouter et que le modelId est "openrouter/auto",
      // on ne devrait pas utiliser l'autorouting
      if (conversation && !conversation.usesAutoRouter && args.modelId === "openrouter/auto") {
        // Dans ce cas, on devrait utiliser un modèle par défaut ou lever une erreur
        // car on ne devrait pas arriver dans cette situation si l'interface utilisateur
        // est correctement implémentée
        console.warn("Conversation non-AutoRouter reçoit une demande avec modelId=openrouter/auto");
        // On pourrait définir un modèle par défaut ici si nécessaire
      }
      
      // Si la conversation utilise AutoRouter, on force l'utilisation d'AutoRouter
      let modelId = args.modelId;
      let modelName = args.modelName;
      
      if (conversation && conversation.usesAutoRouter) {
        modelId = "openrouter/auto";
        modelName = "AutoRouter";
      }

      // Récupère l'historique des messages de la conversation
      const messages: Doc<"messages">[] = await ctx.runQuery(internal.messages.listInternal, {
        conversationId: args.conversationId,
      });

      // Prépare les messages formatés avec un message système au début
      const formattedMessages: Message[] = [
        {
          role: "system",
          content: "Tu es un assistant IA utile et concis. Réponds aux questions de l'utilisateur de manière claire et précise."
        }
      ];

      // Ajoute les messages de la conversation
      messages.forEach((message: Doc<"messages">) => {
        formattedMessages.push({
          role: message.role as "user" | "assistant",
          content: message.content,
        });
      });

      // Vérifie si la clé API OpenRouter est configurée
      if (!process.env.OPENROUTER_API_KEY) {
        console.warn("La clé API OpenRouter n'est pas configurée. Utilisation du mode simulation.");

        // Sauvegarde un message d'avertissement
        await ctx.runMutation(internal.messages.saveAssistantMessage, {
          conversationId: args.conversationId,
          content: "⚠️ La clé API OpenRouter n'est pas configurée. Veuillez configurer la variable d'environnement OPENROUTER_API_KEY dans le dashboard Convex.",
          modelId: modelId,
          modelName: modelName,
        });

        return await simulateResponse(ctx, args);
      }

      // Appelle l'API OpenRouter avec le modelId potentiellement modifié
      return await ctx.runAction(api.openrouter.callOpenRouter, {
        conversationId: args.conversationId,
        modelId: modelId,
        modelName: modelName,
        messages: formattedMessages,
      });
    } catch (error) {
      console.error("Erreur lors de la génération de la réponse:", error);
      return await simulateResponse(ctx, args);
    }
  },
});

// Fonction de simulation pour les tests ou en cas d'erreur
async function simulateResponse(
  ctx: any,
  args: { conversationId: string, modelId: string, modelName?: string }
): Promise<{ success: boolean; simulated: boolean }> {
  // Utilise le nom du modèle fourni ou extrait-le à partir de l'ID
  let displayName = args.modelName;
  if (!displayName) {
    const modelParts = args.modelId.split('/');
    displayName = modelParts.length > 1 ? modelParts[1] : args.modelId;
  }

  // Crée un message en streaming
  const messageId = await ctx.runMutation(internal.messages.startStreamingMessage, {
    conversationId: args.conversationId,
    modelId: args.modelId,
    modelName: args.modelName,
  });

  // Prépare la réponse complète
  const fullResponse = `[Mode Simulation] Ceci est une réponse simulée du modèle ${displayName}.

Je simule une réponse intelligente à votre message.

⚠️ **Configuration requise** : Pour utiliser les modèles réels, vous devez :
1. Configurer la variable d'environnement OPENROUTER_API_KEY dans le dashboard Convex
2. Activer l'entraînement des prompts dans les paramètres de confidentialité d'OpenRouter: https://openrouter.ai/settings/privacy`;

  // Divise la réponse en morceaux pour simuler le streaming
  const chunks = fullResponse.split(' ');

  // Simule le streaming en envoyant les morceaux un par un
  for (let i = 0; i < chunks.length; i++) {
    // Ajoute un espace après chaque mot sauf le dernier
    const chunk = chunks[i] + (i < chunks.length - 1 ? ' ' : '');

    // Ajoute un délai aléatoire pour simuler la vitesse de génération
    await new Promise((resolve) => setTimeout(resolve, Math.random() * 50 + 20));

    // Ajoute le morceau au message
    await ctx.runMutation(internal.messages.appendToStreamingMessage, {
      messageId,
      contentToAppend: chunk,
    });
  }

  // Finalise le message
  await ctx.runMutation(internal.messages.finishStreamingMessage, {
    messageId,
  });

  return {
    success: true,
    simulated: true,
  };
}





