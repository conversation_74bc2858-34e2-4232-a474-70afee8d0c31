import React, { useState, useRef, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useTheme } from '../contexts/ThemeContext';
import DemoNavigation from '../components/common/DemoNavigation';
import logo from '../assets/logo3.svg';
import ModularClaudeInterface from '../components/features/chat/ClaudeInterface';
import '../styles/claudeInterface.css';

/**
 * Interface for the ClaudeInterface component props
 */
interface ClaudeInterfaceProps {}

/**
 * Claude.ai inspired interface component
 */
const ClaudeInterface: React.FC<ClaudeInterfaceProps> = () => {
  // State management
  const [userMessage, setUserMessage] = useState('');
  const [chatStarted, setChatStarted] = useState(false);
  const [assistantResponse, setAssistantResponse] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [activeButton, setActiveButton] = useState('new'); // 'new', 'chat', or 'settings'

  // Refs
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const modelSelectionRef = useRef<HTMLDivElement>(null);
  const bottomRef = useRef<HTMLDivElement>(null);

  // Get theme from context
  const { isDarkMode } = useTheme();

  // Textarea auto-resize
  useEffect(() => {
    if (textareaRef.current) {
      const adjustHeight = () => {
        const textarea = textareaRef.current;
        if (textarea) {
          textarea.style.height = 'auto';
          const newHeight = Math.min(textarea.scrollHeight, 300);
          textarea.style.height = `${newHeight}px`;
        }
      };

      adjustHeight(); // Initial adjustment

      const currentTextarea = textareaRef.current;
      currentTextarea.addEventListener('input', adjustHeight);

      return () => {
        if (currentTextarea) {
          currentTextarea.removeEventListener('input', adjustHeight);
        }
      };
    }
  }, [textareaRef, userMessage, chatStarted]);

  // Show/hide model selection based on input
  useEffect(() => {
    if (modelSelectionRef.current) {
      if (userMessage.trim().length > 0) {
        modelSelectionRef.current.classList.remove('hidden');
      } else {
        modelSelectionRef.current.classList.add('hidden');
      }
    }
  }, [userMessage]);

  // Scroll to bottom when AI responds
  useEffect(() => {
    if (bottomRef.current && chatStarted) {
      bottomRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [assistantResponse, chatStarted]);

  // Handle sidebar button clicks
  const handleSidebarClick = (buttonType: string) => {
    setActiveButton(buttonType);

    if (buttonType === 'new') {
      // Start a new conversation
      setChatStarted(false);
      setUserMessage('');
      setAssistantResponse('');
    } else if (buttonType === 'chat') {
      // Show chat history (simulated)
      if (!chatStarted) {
        setChatStarted(true);
        setAssistantResponse("Voici l'historique de vos conversations. Vous pouvez reprendre une conversation ou en démarrer une nouvelle.");
      }
    } else if (buttonType === 'settings') {
      // Show settings (simulated)
      setChatStarted(true);
      setAssistantResponse("Paramètres de l'application : Vous pouvez configurer vos préférences ici.");
    }
  };

  // Handle starting a chat
  const startChat = async () => {
    if (!userMessage.trim() || isLoading) return;

    setIsLoading(true);
    setChatStarted(true);

    // Clear input
    setUserMessage('');
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
    }

    // Simulate API response
    setTimeout(() => {
      setAssistantResponse("Bonjour ! Je suis votre assistant IA. Comment puis-je vous aider aujourd'hui ?");
      setIsLoading(false);
    }, 2000);
  };

  // Handle key press in textarea
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      if (userMessage.trim() && !isLoading) {
        startChat();
      }
    }
  };

  // Render the welcome screen
  const renderWelcomeScreen = () => (
    <div className="flex flex-col flex-1 justify-center items-center px-4">
      <div className="flex flex-col items-center w-full max-w-3xl">
        {/* Empty chat state */}
        <div className="flex flex-col items-center justify-center mb-8">
          <div className="flex justify-center mb-6">
            <img src={logo} alt="UnPourTous Logo" className="w-24 h-24" />
          </div>
          <p className="text-gray-400 text-center text-lg font-body mb-4">Je suis là pour discuter, aider ou simplement écouter</p>
        </div>

        {/* Input area - centered on page */}
        {renderInputArea()}
      </div>
    </div>
  );

  // Render the chat screen after starting a conversation
  const renderChatScreen = () => (
    <>
      <div className="flex-1 overflow-y-auto py-4 px-4">
        <div className="max-w-3xl mx-auto">
          <div className="mb-6 bg-claude-light-gray/20 rounded-xl p-4">
            <div className="font-medium mb-1 text-gray-400 font-body">Vous</div>
            <div className="text-white font-body">{userMessage || "Comment puis-je vous aider ?"}</div>
          </div>
          <div className="bg-claude-light-gray/45 rounded-xl p-4">
            <div className="font-medium mb-1 text-gray-400 font-body">Assistant IA</div>
            {isLoading ? (
              <div className="flex items-center">
                <span className="loader"></span>
              </div>
            ) : (
              <div className="text-white font-body">{assistantResponse}</div>
            )}
          </div>
          <div ref={bottomRef}></div>
        </div>
      </div>
      <div className="px-4 pb-4">
        {renderInputArea()}
      </div>
    </>
  );

  // Render the input area (used in both welcome and chat screens)
  const renderInputArea = () => (
    <>
      <div className="relative w-full rounded-xl border border-claude-light-gray bg-claude-gray p-2 shadow-sm">
        <textarea
          ref={textareaRef}
          value={userMessage}
          onChange={(e) => setUserMessage(e.target.value)}
          onKeyDown={handleKeyDown}
          className="w-full bg-transparent outline-none resize-none text-base py-2 px-3 h-12 max-h-60 font-body"
          placeholder="Comment puis-je vous aider ?"
          rows={1}
        />
        <div className="flex justify-between px-2 pt-1">
          <div className="flex items-center">
            <button type="button" className="p-1.5 rounded-lg text-gray-400 hover:bg-claude-light-gray/30 hover:text-white">
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-5 h-5">
                <path strokeLinecap="round" strokeLinejoin="round" d="M12 4.5v15m7.5-7.5h-15" />
              </svg>
            </button>
            <button type="button" className="p-1.5 rounded-lg text-gray-400 hover:bg-claude-light-gray/30 hover:text-white">
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-5 h-5">
                <path strokeLinecap="round" strokeLinejoin="round" d="M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25m0 12.75h7.5m-7.5 3H12M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 00-9-9z" />
              </svg>
            </button>
          </div>
          <div className="flex items-center gap-3">
            <div ref={modelSelectionRef} className="text-sm text-gray-400 hidden">
              <select className="bg-claude-gray border-none text-white cursor-pointer p-1 rounded font-body">
                <option>Claude 3.7 Sonnet</option>
                <option>Claude 3 Haiku</option>
                <option>Claude 3 Opus</option>
                <option>GPT-4</option>
                <option>Mistral Large</option>
              </select>
            </div>
            {/* Auto Router Button */}
            <button type="button" className="p-1.5 rounded-lg text-white bg-primary opacity-80 hover:opacity-100">
              <img src={logo} alt="UnPourTous Logo" className="w-5 h-5" />
            </button>
            <button
              type="button"
              onClick={startChat}
              disabled={!userMessage.trim() || isLoading}
              className="p-1.5 text-white rounded-lg bg-claude-orange opacity-80 hover:opacity-100 disabled:opacity-50"
            >
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-5 h-5">
                <path strokeLinecap="round" strokeLinejoin="round" d="M6 12L3.269 3.126A59.768 59.768 0 0121.485 12 59.77 59.77 0 013.27 20.876L5.999 12zm0 0h7.5" />
              </svg>
            </button>
          </div>
        </div>
      </div>

      {/* Categories */}
      <div className="flex justify-center mt-4 flex-wrap gap-2 font-body">
        <button className="px-4 py-2 rounded-lg bg-claude-gray text-sm flex items-center gap-2 hover:bg-claude-light-gray/70">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-5 h-5">
            <path strokeLinecap="round" strokeLinejoin="round" d="M20.25 8.511c.884.284 1.5 1.128 1.5 2.097v4.286c0 1.136-.847 2.1-1.98 2.193-.34.027-.68.052-1.02.072v3.091l-3-3c-1.354 0-2.694-.055-4.02-.163a2.115 2.115 0 01-.825-.242m9.345-8.334a2.126 2.126 0 00-.476-.095 48.64 48.64 0 00-8.048 0c-1.131.094-1.976 1.057-1.976 2.192v4.286c0 .837.46 1.58 1.155 1.951m9.345-8.334V6.637c0-1.621-1.152-3.026-2.76-3.235A48.455 48.455 0 0011.25 3c-2.115 0-4.198.137-6.24.402-1.608.209-2.76 1.614-2.76 3.235v6.226c0 1.621 1.152 3.026 2.76 3.235.577.075 1.157.14 1.74.194V21l4.155-4.155" />
          </svg>
          Discussion
        </button>
        <button className="px-4 py-2 rounded-lg bg-claude-gray text-sm flex items-center gap-2 hover:bg-claude-light-gray/70">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-5 h-5">
            <path strokeLinecap="round" strokeLinejoin="round" d="M21 21l-5.197-5.197m0 0A7.5 7.5 0 105.196 5.196a7.5 7.5 0 0010.607 10.607z" />
          </svg>
          Recherche Web
        </button>
        <button className="px-4 py-2 rounded-lg bg-claude-gray text-sm flex items-center gap-2 hover:bg-claude-light-gray/70">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-5 h-5">
            <path strokeLinecap="round" strokeLinejoin="round" d="M12 18v-5.25m0 0a6.01 6.01 0 001.5-.189m-1.5.189a6.01 6.01 0 01-1.5-.189m3.75 7.478a12.06 12.06 0 01-4.5 0m3.75 2.383a14.406 14.406 0 01-3 0M14.25 18v-.192c0-.983.658-1.823 1.508-2.316a7.5 7.5 0 10-7.517 0c.85.493 1.509 1.333 1.509 2.316V18" />
          </svg>
          Raisonnement
        </button>
      </div>
    </>
  );

  return (
    <div className={`claude-interface h-screen flex flex-col overflow-hidden ${isDarkMode ? 'dark bg-claude-dark text-gray-200' : 'bg-white text-gray-800'}`}>
      <DemoNavigation />
      <div className="flex flex-1 overflow-hidden mt-10">
        {/* Left sidebar */}
        <div className="w-16 border-r border-claude-light-gray flex flex-col items-center py-4 hidden sm:flex">
          <div className="flex flex-col items-center flex-grow">
            <button
              className="w-8 h-8 mb-6 flex items-center justify-center hover:bg-claude-gray/50 rounded-full transition-colors"
              title="Menu"
            >
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-6 h-6">
                <path strokeLinecap="round" strokeLinejoin="round" d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5" />
              </svg>
            </button>

            <button
              className={`w-8 h-8 ${activeButton === 'new' ? 'bg-claude-orange' : 'bg-claude-gray hover:bg-claude-light-gray/70'} text-white rounded-full flex items-center justify-center mb-4 relative transition-colors`}
              onClick={() => handleSidebarClick('new')}
              title="Nouvelle conversation"
            >
              <img src={logo} alt="UnPourTous Logo" className="w-5 h-5 absolute" />
            </button>

            <button
              className={`w-8 h-8 ${activeButton === 'chat' ? 'bg-claude-orange' : 'bg-claude-gray hover:bg-claude-light-gray/70'} text-white rounded-full flex items-center justify-center mb-4 transition-colors`}
              onClick={() => handleSidebarClick('chat')}
              title="Conversations"
            >
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-5 h-5">
                <path strokeLinecap="round" strokeLinejoin="round" d="M20.25 8.511c.884.284 1.5 1.128 1.5 2.097v4.286c0 1.136-.847 2.1-1.98 2.193-.34.027-.68.052-1.02.072v3.091l-3-3c-1.354 0-2.694-.055-4.02-.163a2.115 2.115 0 01-.825-.242m9.345-8.334a2.126 2.126 0 00-.476-.095 48.64 48.64 0 00-8.048 0c-1.131.094-1.976 1.057-1.976 2.192v4.286c0 .837.46 1.58 1.155 1.951m9.345-8.334V6.637c0-1.621-1.152-3.026-2.76-3.235A48.455 48.455 0 0011.25 3c-2.115 0-4.198.137-6.24.402-1.608.209-2.76 1.614-2.76 3.235v6.226c0 1.621 1.152 3.026 2.76 3.235.577.075 1.157.14 1.74.194V21l4.155-4.155" />
              </svg>
            </button>
          </div>

          <div className="mt-auto">
            <button
              className={`w-8 h-8 ${activeButton === 'settings' ? 'bg-claude-orange' : 'bg-claude-gray hover:bg-claude-light-gray/70'} text-white rounded-full flex items-center justify-center transition-colors`}
              onClick={() => handleSidebarClick('settings')}
              title="Paramètres"
            >
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-5 h-5">
                <path strokeLinecap="round" strokeLinejoin="round" d="M9.594 3.94c.09-.542.56-.94 1.11-.94h2.593c.55 0 1.02.398 1.11.94l.213 1.281c.063.374.313.686.645.87.074.04.147.083.22.127.324.196.72.257 1.075.124l1.217-.456a1.125 1.125 0 011.37.49l1.296 2.247a1.125 1.125 0 01-.26 1.431l-1.003.827c-.293.24-.438.613-.431.992a6.759 6.759 0 010 .255c-.007.378.138.75.43.99l1.005.828c.424.35.534.954.26 1.43l-1.298 2.247a1.125 1.125 0 01-1.369.491l-1.217-.456c-.355-.133-.75-.072-1.076.124a6.57 6.57 0 01-.22.128c-.331.183-.581.495-.644.869l-.213 1.28c-.09.543-.56.941-1.11.941h-2.594c-.55 0-1.02-.398-1.11-.94l-.213-1.281c-.062-.374-.312-.686-.644-.87a6.52 6.52 0 01-.22-.127c-.325-.196-.72-.257-1.076-.124l-1.217.456a1.125 1.125 0 01-1.369-.49l-1.297-2.247a1.125 1.125 0 01.26-1.431l1.004-.827c.292-.24.437-.613.43-.992a6.932 6.932 0 010-.255c.007-.378-.138-.75-.43-.99l-1.004-.828a1.125 1.125 0 01-.26-1.43l1.297-2.247a1.125 1.125 0 011.37-.491l1.216.456c.356.133.751.072 1.076-.124.072-.044.146-.087.22-.128.332-.183.582-.495.644-.869l.214-1.281z" />
                <path strokeLinecap="round" strokeLinejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
              </svg>
            </button>
          </div>
        </div>

        {/* Main content */}
        <div className="flex-1 flex flex-col h-full overflow-hidden">
          {/* Render either welcome screen or chat screen based on state */}
          {!chatStarted ? renderWelcomeScreen() : renderChatScreen()}
        </div>
      </div>
    </div>
  );
};

/**
 * Page ClaudeInterfacePage - Utilise le composant modulaire ClaudeInterface
 */
const ClaudeInterfacePage: React.FC = () => {
  const navigate = useNavigate();

  // Fonction appelée lorsqu'une nouvelle conversation est créée
  const handleConversationCreated = (conversationId: any) => {
    // Rediriger vers la page de conversation
    navigate(`/c/${conversationId}`);
  };

  return (
    <div className="h-screen flex flex-col bg-claude-dark text-white">
      <DemoNavigation />
      <div className="flex-1 overflow-hidden">
        <ModularClaudeInterface onConversationCreated={handleConversationCreated} />
      </div>
    </div>
  );
};

// Exporter le composant original pour la compatibilité
export default ClaudeInterface;

// Exporter le nouveau composant modulaire
export { ClaudeInterfacePage };
