<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Chat Assistant</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: '#5D5CDE',
                        'claude-orange': '#FF8E6E',
                        'claude-dark': '#1A1A1A',
                        'claude-darker': '#141414',
                        'claude-gray': '#333333',
                        'claude-light-gray': '#444444',
                    },
                }
            }
        }
    </script>
    <style>
        .loader {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            display: inline-block;
            position: relative;
            border: 2px solid #FF8E6E;
            animation: rotation 1s linear infinite;
        }
        
        .loader:after {
            content: '';
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
            width: 40px;
            height: 40px;
            border-radius: 50%;
            border: 2px solid;
            border-color: #5D5CDE transparent;
        }
        
        @keyframes rotation {
            0% {
                transform: rotate(0deg);
            }
            100% {
                transform: rotate(360deg);
            }
        }

        ::-webkit-scrollbar {
            width: 6px;
            height: 6px;
        }
        
        ::-webkit-scrollbar-track {
            background: transparent;
        }
        
        ::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.15);
            border-radius: 3px;
        }
        
        ::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.3);
        }
        
        .dark ::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.15);
        }
        
        .dark ::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.3);
        }
    </style>
</head>
<body class="dark bg-claude-dark text-gray-200 h-screen flex overflow-hidden">
    <!-- Left sidebar -->
    <div class="w-16 border-r border-claude-light-gray flex flex-col items-center py-4 hidden sm:flex">
        <button class="w-8 h-8 mb-6 flex items-center justify-center">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6">
                <path stroke-linecap="round" stroke-linejoin="round" d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5" />
            </svg>
        </button>

        <button class="w-8 h-8 bg-claude-orange text-white rounded-full flex items-center justify-center mb-4">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5">
                <path stroke-linecap="round" stroke-linejoin="round" d="M12 4.5v15m7.5-7.5h-15" />
            </svg>
        </button>

        <button class="w-8 h-8 text-gray-400 rounded-full flex items-center justify-center mb-4">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5">
                <path stroke-linecap="round" stroke-linejoin="round" d="M20.25 8.511c.884.284 1.5 1.128 1.5 2.097v4.286c0 1.136-.847 2.1-1.98 2.193-.34.027-.68.052-1.02.072v3.091l-3-3c-1.354 0-2.694-.055-4.02-.163a2.115 2.115 0 01-.825-.242m9.345-8.334a2.126 2.126 0 00-.476-.095 48.64 48.64 0 00-8.048 0c-1.131.094-1.976 1.057-1.976 2.192v4.286c0 .837.46 1.58 1.155 1.951m9.345-8.334V6.637c0-1.621-1.152-3.026-2.76-3.235A48.455 48.455 0 0011.25 3c-2.115 0-4.198.137-6.24.402-1.608.209-2.76 1.614-2.76 3.235v6.226c0 1.621 1.152 3.026 2.76 3.235.577.075 1.157.14 1.74.194V21l4.155-4.155" />
            </svg>
        </button>
    </div>

    <!-- Main content -->
    <div class="flex-1 flex flex-col h-full overflow-hidden">
        <!-- Subscription info -->
        <div class="flex justify-center mt-4">
            <div class="px-4 py-1.5 bg-claude-gray rounded-lg flex items-center gap-2">
                <span class="text-sm">Free Plan</span>
                <span class="text-primary text-sm">•</span>
                <a href="#" class="text-primary text-sm">Upgrade</a>
            </div>
        </div>

        <!-- Main content area with centered elements -->
        <div class="flex flex-col flex-1 justify-center items-center px-4">
            <div class="flex flex-col items-center w-full max-w-3xl">
                <!-- Empty chat state -->
                <div class="flex flex-col items-center justify-center mb-8">
                    <div class="flex justify-center mb-4">
                        <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="w-12 h-12 text-claude-orange">
                            <path d="M12 17V17.5V18" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
                            <path d="M12 14L12 6" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
                            <path d="M8.5 9.5L12 6L15.5 9.5" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z" stroke="currentColor" stroke-width="1.5"/>
                        </svg>
                    </div>
                    <h1 class="text-4xl font-light text-center mb-2">How was your day?</h1>
                    <p class="text-gray-400 text-center text-lg">I'm here to chat, help, or just listen</p>
                </div>

                <!-- Input area - centered on page -->
                <div class="w-full">
                    <div class="relative w-full rounded-xl border border-claude-light-gray bg-claude-gray p-2 shadow-sm">
                        <textarea id="userInput" 
                            class="w-full bg-transparent outline-none resize-none text-base py-2 px-3 h-12 max-h-60"
                            placeholder="How can I help you?"
                            rows="1"></textarea>
                        <div class="flex justify-between px-2 pt-1">
                            <div class="flex items-center">
                                <button class="p-1.5 rounded-lg text-gray-400 hover:bg-claude-light-gray/30 hover:text-white">
                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M12 4.5v15m7.5-7.5h-15" />
                                    </svg>
                                </button>
                                <button class="p-1.5 rounded-lg text-gray-400 hover:bg-claude-light-gray/30 hover:text-white">
                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25m0 12.75h7.5m-7.5 3H12M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 00-9-9z" />
                                    </svg>
                                </button>
                            </div>
                            <div class="flex items-center gap-3">
                                <div class="text-sm text-gray-400 hidden" id="modelSelection">
                                    <select class="bg-claude-gray border-none text-white cursor-pointer p-1 rounded">
                                        <option>Claude 3.7 Sonnet</option>
                                        <option>Claude 3 Haiku</option>
                                        <option>Claude 3 Opus</option>
                                    </select>
                                </div>
                                <!-- Auto Router Button -->
                                <button class="p-1.5 rounded-lg text-white bg-claude-orange opacity-80 hover:opacity-100">
                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M7.5 21L3 16.5m0 0L7.5 12M3 16.5h13.5m0-13.5L21 7.5m0 0L16.5 12M21 7.5H7.5" />
                                    </svg>
                                </button>
                                <button id="sendButton" class="p-1.5 text-white rounded-lg bg-primary opacity-80 hover:opacity-100 disabled:opacity-50">
                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M6 12L3.269 3.126A59.768 59.768 0 0121.485 12 59.77 59.77 0 013.27 20.876L5.999 12zm0 0h7.5" />
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Categories -->
                    <div class="flex justify-center mt-4 flex-wrap gap-2">
                        <button class="px-4 py-2 rounded-lg bg-claude-gray text-sm flex items-center gap-2 hover:bg-claude-light-gray/70">
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M20.25 8.511c.884.284 1.5 1.128 1.5 2.097v4.286c0 1.136-.847 2.1-1.98 2.193-.34.027-.68.052-1.02.072v3.091l-3-3c-1.354 0-2.694-.055-4.02-.163a2.115 2.115 0 01-.825-.242m9.345-8.334a2.126 2.126 0 00-.476-.095 48.64 48.64 0 00-8.048 0c-1.131.094-1.976 1.057-1.976 2.192v4.286c0 .837.46 1.58 1.155 1.951m9.345-8.334V6.637c0-1.621-1.152-3.026-2.76-3.235A48.455 48.455 0 0011.25 3c-2.115 0-4.198.137-6.24.402-1.608.209-2.76 1.614-2.76 3.235v6.226c0 1.621 1.152 3.026 2.76 3.235.577.075 1.157.14 1.74.194V21l4.155-4.155" />
                            </svg>
                            Chat
                        </button>
                        <button class="px-4 py-2 rounded-lg bg-claude-gray text-sm flex items-center gap-2 hover:bg-claude-light-gray/70">
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M21 21l-5.197-5.197m0 0A7.5 7.5 0 105.196 5.196a7.5 7.5 0 0010.607 10.607z" />
                            </svg>
                            Web Search
                        </button>
                        <button class="px-4 py-2 rounded-lg bg-claude-gray text-sm flex items-center gap-2 hover:bg-claude-light-gray/70">
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M12 18v-5.25m0 0a6.01 6.01 0 001.5-.189m-1.5.189a6.01 6.01 0 01-1.5-.189m3.75 7.478a12.06 12.06 0 01-4.5 0m3.75 2.383a14.406 14.406 0 01-3 0M14.25 18v-.192c0-.983.658-1.823 1.508-2.316a7.5 7.5 0 10-7.517 0c.85.493 1.509 1.333 1.509 2.316V18" />
                            </svg>
                            Reasoning
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Dark mode detection
        if (window.matchMedia && window.matchMedia('(prefers-color-scheme: light)').matches) {
            document.documentElement.classList.remove('dark');
            document.body.classList.remove('bg-claude-dark');
            document.body.classList.add('bg-white', 'text-gray-800');
        }
        
        window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', event => {
            if (event.matches) {
                document.documentElement.classList.add('dark');
                document.body.classList.add('bg-claude-dark');
                document.body.classList.remove('bg-white', 'text-gray-800');
            } else {
                document.documentElement.classList.remove('dark');
                document.body.classList.remove('bg-claude-dark');
                document.body.classList.add('bg-white', 'text-gray-800');
            }
        });

        // Textarea auto-resize
        const textarea = document.getElementById('userInput');
        textarea.addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = (this.scrollHeight) + 'px';
            
            // Show model selection when user starts typing
            if (this.value.trim().length > 0) {
                document.getElementById('modelSelection').classList.remove('hidden');
            } else {
                document.getElementById('modelSelection').classList.add('hidden');
            }
        });

        // Initiate chat on send button click or Enter key (without Shift)
        const sendButton = document.getElementById('sendButton');
        
        sendButton.addEventListener('click', startChat);
        textarea.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                startChat();
            }
        });

        async function startChat() {
            const userMessage = textarea.value.trim();
            if (!userMessage) return;
            
            // Clear the input field and hide model selection
            textarea.value = '';
            textarea.style.height = 'auto';
            document.getElementById('modelSelection').classList.add('hidden');
            
            // Here you can add code to actually send the message to your AI
            // and handle the response. For now, we'll just clear the welcome screen
            // and show a simple chat UI with loading indicator
            
            const mainContent = document.querySelector('.flex-1.flex.flex-col');
            mainContent.innerHTML = `
                <div class="flex-1 overflow-y-auto py-4 px-4">
                    <div class="max-w-3xl mx-auto">
                        <div class="mb-6">
                            <div class="font-medium mb-1 text-gray-400">You</div>
                            <div class="text-white">${userMessage}</div>
                        </div>
                        <div>
                            <div class="font-medium mb-1 text-gray-400">Assistant</div>
                            <div class="flex items-center">
                                <span class="loader"></span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="px-4 pb-4">
                    <div class="relative max-w-3xl mx-auto w-full rounded-xl border border-claude-light-gray bg-claude-gray p-2 shadow-sm">
                        <textarea id="userInput" 
                            class="w-full bg-transparent outline-none resize-none text-base py-2 px-3 h-12 max-h-60"
                            placeholder="How can I help you?"
                            rows="1"></textarea>
                        <div class="flex justify-between px-2 pt-1">
                            <div class="flex items-center">
                                <button class="p-1.5 rounded-lg text-gray-400 hover:bg-claude-light-gray/30 hover:text-white">
                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M12 4.5v15m7.5-7.5h-15" />
                                    </svg>
                                </button>
                                <button class="p-1.5 rounded-lg text-gray-400 hover:bg-claude-light-gray/30 hover:text-white">
                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25m0 12.75h7.5m-7.5 3H12M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 00-9-9z" />
                                    </svg>
                                </button>
                            </div>
                            <div class="flex items-center gap-3">
                                <div class="text-sm text-gray-400 hidden" id="modelSelection">
                                    <select class="bg-claude-gray border-none text-white cursor-pointer p-1 rounded">
                                        <option>Claude 3.7 Sonnet</option>
                                        <option>Claude 3 Haiku</option>
                                        <option>Claude 3 Opus</option>
                                    </select>
                                </div>
                                <!-- Auto Router Button -->
                                <button class="p-1.5 rounded-lg text-white bg-claude-orange opacity-80 hover:opacity-100">
                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M7.5 21L3 16.5m0 0L7.5 12M3 16.5h13.5m0-13.5L21 7.5m0 0L16.5 12M21 7.5H7.5" />
                                    </svg>
                                </button>
                                <button id="sendButton" class="p-1.5 text-white rounded-lg bg-primary opacity-80 hover:opacity-100 disabled:opacity-50">
                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M6 12L3.269 3.126A59.768 59.768 0 0121.485 12 59.77 59.77 0 013.27 20.876L5.999 12zm0 0h7.5" />
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Categories -->
                    <div class="flex justify-center mt-4 flex-wrap gap-2">
                        <button class="px-4 py-2 rounded-lg bg-claude-gray text-sm flex items-center gap-2 hover:bg-claude-light-gray/70">
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M20.25 8.511c.884.284 1.5 1.128 1.5 2.097v4.286c0 1.136-.847 2.1-1.98 2.193-.34.027-.68.052-1.02.072v3.091l-3-3c-1.354 0-2.694-.055-4.02-.163a2.115 2.115 0 01-.825-.242m9.345-8.334a2.126 2.126 0 00-.476-.095 48.64 48.64 0 00-8.048 0c-1.131.094-1.976 1.057-1.976 2.192v4.286c0 .837.46 1.58 1.155 1.951m9.345-8.334V6.637c0-1.621-1.152-3.026-2.76-3.235A48.455 48.455 0 0011.25 3c-2.115 0-4.198.137-6.24.402-1.608.209-2.76 1.614-2.76 3.235v6.226c0 1.621 1.152 3.026 2.76 3.235.577.075 1.157.14 1.74.194V21l4.155-4.155" />
                            </svg>
                            Chat
                        </button>
                        <button class="px-4 py-2 rounded-lg bg-claude-gray text-sm flex items-center gap-2 hover:bg-claude-light-gray/70">
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M21 21l-5.197-5.197m0 0A7.5 7.5 0 105.196 5.196a7.5 7.5 0 0010.607 10.607z" />
                            </svg>
                            Web Search
                        </button>
                        <button class="px-4 py-2 rounded-lg bg-claude-gray text-sm flex items-center gap-2 hover:bg-claude-light-gray/70">
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M12 18v-5.25m0 0a6.01 6.01 0 001.5-.189m-1.5.189a6.01 6.01 0 01-1.5-.189m3.75 7.478a12.06 12.06 0 01-4.5 0m3.75 2.383a14.406 14.406 0 01-3 0M14.25 18v-.192c0-.983.658-1.823 1.508-2.316a7.5 7.5 0 10-7.517 0c.85.493 1.509 1.333 1.509 2.316V18" />
                            </svg>
                            Reasoning
                        </button>
                    </div>
                </div>
            `;
            
            // Reattach event listeners to the new elements
            const newTextarea = document.getElementById('userInput');
            const newSendButton = document.getElementById('sendButton');
            
            newTextarea.addEventListener('input', function() {
                this.style.height = 'auto';
                this.style.height = (this.scrollHeight) + 'px';
                
                if (this.value.trim().length > 0) {
                    document.getElementById('modelSelection').classList.remove('hidden');
                } else {
                    document.getElementById('modelSelection').classList.add('hidden');
                }
            });
            
            newSendButton.addEventListener('click', startChat);
            newTextarea.addEventListener('keydown', function(e) {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    startChat();
                }
            });

            // Here you would typically integrate with a real AI API
            // For this example, we'll simulate a response after 2 seconds
            setTimeout(() => {
                simulateResponse("Hello! I'm your AI assistant. How can I help you today?");
            }, 2000);
        }

        function simulateResponse(message) {
            const assistantContainer = document.querySelector('.flex-1.overflow-y-auto .max-w-3xl > div:last-child');
            assistantContainer.innerHTML = `
                <div class="font-medium mb-1 text-gray-400">Assistant</div>
                <div class="text-white">${message}</div>
            `;
        }
    </script>
</body>
</html>