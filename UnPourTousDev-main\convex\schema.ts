import { defineSchema, defineTable } from "convex/server";
import { v } from "convex/values";
import { authTables } from "@convex-dev/auth/server";

export default defineSchema({
  ...authTables,
  userPreferences: defineTable({
    userId: v.string(),
    interfaceType: v.optional(v.string()), // "original" ou "claude"
    theme: v.optional(v.string()), // "light" ou "dark"
    // Autres préférences futures...
  }).index("by_user", ["userId"]),
  folders: defineTable({
    userId: v.string(),
    name: v.string(),
    color: v.optional(v.string()),
    order: v.optional(v.number()),
    parentFolderId: v.optional(v.id("folders")),
  })
    .index("by_user", ["userId"])
    .index("by_parent", ["parentFolderId"]),

  conversations: defineTable({
    userId: v.string(),
    title: v.string(),
    folderId: v.optional(v.id("folders")),
    order: v.optional(v.number()),
    usesAutoRouter: v.optional(v.boolean()),
    modelId: v.optional(v.string()),
  })
    .index("by_user", ["userId"])
    .index("by_folder", ["folderId"]),

  messages: defineTable({
    conversationId: v.id("conversations"),
    role: v.union(v.literal("user"), v.literal("assistant")),
    content: v.string(),
    modelUsed: v.string(),
    modelName: v.optional(v.string()),
    isStreaming: v.optional(v.boolean()),
    references: v.optional(v.array(v.object({
      id: v.string(),
      title: v.string(),
      url: v.string(),
      description: v.optional(v.string()),
      image: v.optional(v.string()),
      favicon: v.optional(v.string()),
      domain: v.optional(v.string()),
    }))),
  }).index("by_conversation", ["conversationId"]),

  models: defineTable({
    name: v.string(),
    modelId: v.string(),
    provider: v.string(),
    description: v.string(),
    enabled: v.boolean(),
    contextLength: v.optional(v.number()),
    pricing: v.optional(
      v.object({
        prompt: v.number(),
        completion: v.number(),
      })
    ),
    custom: v.optional(v.boolean()),
    webSearch: v.optional(v.boolean()),
    structuredOutput: v.optional(v.boolean()),
    reasoning: v.optional(v.boolean()),
    streaming: v.optional(v.boolean()),
    chat: v.optional(v.boolean()),
  }),

  // Table pour les modèles en production (live)
  livemodels: defineTable({
    name: v.string(),
    modelId: v.string(),
    provider: v.string(),
    description: v.string(),
    contextLength: v.optional(v.number()),
    pricing: v.optional(
      v.object({
        prompt: v.number(),
        completion: v.number(),
      })
    ),
    custom: v.optional(v.boolean()),
    webSearch: v.optional(v.boolean()),
    structuredOutput: v.optional(v.boolean()),
    reasoning: v.optional(v.boolean()),
    streaming: v.optional(v.boolean()),
    chat: v.optional(v.boolean()),
    enabled: v.optional(v.boolean()),
  })
    .index("by_name", ["name"])
    .index("by_provider", ["provider"])
    .index("by_model_id", ["modelId"]),
});
