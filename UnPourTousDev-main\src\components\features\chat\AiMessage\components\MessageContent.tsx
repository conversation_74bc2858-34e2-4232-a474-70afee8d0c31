import React from "react";
import { MessageContentProps } from "../types";
import SourcesList from "./SourcesList";
import MarkdownRenderer from "../../../../common/MarkdownRenderer";

/**
 * Composant pour le contenu d'un message IA
 */
const MessageContent: React.FC<MessageContentProps> = ({
  content,
  references,
  isStreaming,
  showCursor
}) => {
  const [showAllSources, setShowAllSources] = React.useState(false);

  return (
    <div className="prose dark:prose-invert prose-sm max-w-none markdown-content w-full break-words whitespace-pre-wrap text-white">
      {/* Affichage des références en haut sous forme de tuiles */}
      {references && references.length > 0 && (
        <SourcesList
          references={references}
          showAllSources={showAllSources}
          setShowAllSources={setShowAllSources}
        />
      )}

      {/* Contenu du message avec liens vers les sources */}
      <div className="text-white font-body">
        <MarkdownRenderer
          content={content}
          references={references}
          isStreaming={isStreaming}
          showCursor={showCursor}
        />
      </div>
    </div>
  );
};

export default MessageContent;
