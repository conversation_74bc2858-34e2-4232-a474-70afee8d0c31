import React from "react";
import { MessageContentProps } from "../types";
import SourcesList from "./SourcesList";
import MarkdownRenderer from "../../../../common/MarkdownRenderer";

/**
 * Composant pour le contenu d'un message IA
 */
const MessageContent: React.FC<MessageContentProps> = ({
  content,
  references,
  isStreaming,
  showCursor
}) => {
  const [showAllSources, setShowAllSources] = React.useState(false);

  // Debug: Log when references change
  React.useEffect(() => {
    if (references && references.length > 0 && !isStreaming) {
      console.log("📋 [MessageContent] Sources disponibles:", references.length, "références");
    }
  }, [references, isStreaming]);

  return (
    <div className="prose dark:prose-invert prose-sm max-w-none markdown-content w-full break-words whitespace-pre-wrap text-white">
      {/* Affichage des références en haut sous forme de tuiles - seulement après la fin du streaming */}
      {references && references.length > 0 && !isStreaming && (
        <SourcesList
          references={references}
          showAllSources={showAllSources}
          setShowAllSources={setShowAllSources}
        />
      )}

      {/* Contenu du message avec liens vers les sources */}
      <div className="text-white font-body">
        <MarkdownRenderer
          content={content}
          references={references}
          isStreaming={isStreaming}
          showCursor={showCursor}
        />
      </div>
    </div>
  );
};

export default MessageContent;
