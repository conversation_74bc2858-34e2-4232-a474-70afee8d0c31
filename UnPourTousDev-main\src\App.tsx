import { useAuth } from "@clerk/clerk-react";
import { BrowserRouter as Router, Route, Routes, Navigate, useLocation, Outlet } from "react-router-dom";
import { useEffect } from "react";
import { ChatLayout, MainLayout } from "./components/layout";
import LoginPage from "./pages/LoginPage";
import { SettingsPage, ThemeColorTester } from "./components/features/settings";
import AvatarDetails from "./pages/DemoTest";
import NewConversationDemo from "./pages/NewConversationDemo";
import ComfyInterface, { ComfyInterfacePage } from "./pages/ComfyInterface";
import EnhancedHomePage from "./pages/EnhancedHomePage";
import InterfaceSelectorPage from "./pages/InterfaceSelectorPage";
import InterfaceRedirect from "./components/features/interface/InterfaceRedirect";
import ConversationPage from "./pages/ConversationPage";
import initMermaidErrorCleaner from "./utils/mermaidErrorCleaner";

// Composant pour vérifier l'authentification et rediriger si nécessaire
function RequireAuth({ children }: { children: JSX.Element }) {
  const { isSignedIn, isLoaded } = useAuth();
  const location = useLocation();

  // Si l'authentification est en cours de chargement, afficher un spinner
  if (!isLoaded) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  // Si l'utilisateur n'est pas connecté, rediriger vers la page de connexion
  if (!isSignedIn) {
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  // Si l'utilisateur est connecté, afficher le contenu protégé
  return children;
}

// Composant pour rediriger les utilisateurs déjà connectés
function RedirectIfAuthenticated({ children }: { children: JSX.Element }) {
  const { isSignedIn, isLoaded } = useAuth();

  // Si l'authentification est en cours de chargement, afficher un spinner
  if (!isLoaded) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  // Si l'utilisateur est déjà connecté, rediriger vers la page d'accueil
  if (isSignedIn) {
    return <Navigate to="/" replace />;
  }

  // Si l'utilisateur n'est pas connecté, afficher la page de connexion
  return children;
}

// Composant principal de l'application
export default function App() {
  const { isSignedIn, isLoaded } = useAuth();

  // Effet pour enregistrer l'état d'authentification dans le localStorage
  useEffect(() => {
    if (isLoaded) {
      localStorage.setItem('isAuthenticated', isSignedIn ? 'true' : 'false');
    }
  }, [isSignedIn, isLoaded]);

  // Initialiser le nettoyeur d'erreurs Mermaid
  useEffect(() => {
    // Initialiser le nettoyeur d'erreurs Mermaid
    const cleanup = initMermaidErrorCleaner();

    // Nettoyer lorsque le composant est démonté
    return () => {
      cleanup();
    };
  }, []);

  // Si l'authentification est en cours de chargement, afficher un spinner
  if (!isLoaded) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <Router>
      <Routes>
        {/* Routes protégées (nécessitent une authentification) */}
        <Route
          path="/interface-selector"
          element={
            <RequireAuth>
              <InterfaceSelectorPage />
            </RequireAuth>
          }
        />

        <Route
          path="/settings"
          element={
            <RequireAuth>
              <SettingsPage />
            </RequireAuth>
          }
        />
        <Route
          path="/theme"
          element={
            <RequireAuth>
              <div className="p-8 bg-surface-light dark:bg-surface-dark min-h-screen">
                <ThemeColorTester />
              </div>
            </RequireAuth>
          }
        />

        {/* Routes publiques */}
        <Route
          path="/login"
          element={
            <RedirectIfAuthenticated>
              <LoginPage />
            </RedirectIfAuthenticated>
          }
        />

        {/* Routes de démonstration - accessibles sans authentification */}
        <Route
          path="/demo"
          element={<AvatarDetails />}
        />
        <Route
          path="/newconversation"
          element={<NewConversationDemo />}
        />
        {/* Routes avec le layout principal (sidebar) */}
        <Route
          path="/"
          element={
            <RequireAuth>
              <MainLayout />
            </RequireAuth>
          }
        >
          {/* Route pour la page d'accueil Comfy */}
          <Route
            path="comfy"
            element={<EnhancedHomePage />}
          />

          {/* Route pour la page d'accueil */}
          <Route
            index
            element={<EnhancedHomePage />}
          />

          {/* Route pour les conversations existantes */}
          <Route
            path="c/:conversationId"
            element={<ConversationPage />}
          />
        </Route>



        {/* Redirection pour les routes inconnues */}
        <Route path="*" element={<Navigate to="/" replace />} />
      </Routes>
    </Router>
  );
}
