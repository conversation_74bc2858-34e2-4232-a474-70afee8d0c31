

interface MoreSourcesTileProps {
  sources: {
    id: string;
    title: string;
    url: string;
  }[];
  onClick: () => void;
}

export default function MoreSourcesTile({ sources, onClick }: MoreSourcesTileProps) {
  // Fonction pour extraire le nom de domaine à partir de l'URL
  const extractDomain = (url: string) => {
    try {
      const domain = new URL(url).hostname;
      return domain.startsWith('www.') ? domain.substring(4) : domain;
    } catch (e) {
      return url;
    }
  };

  // Prendre jusqu'à 6 premières sources pour afficher leurs favicons
  const previewSources = sources.slice(0, Math.min(6, sources.length));

  return (
    <button
      onClick={onClick}
      className="more-sources-tile block p-1 rounded-lg border border-tango-300 bg-tango-100/30 hover:bg-tango-200/40 hover:border-tango-400 transition-colors duration-150 text-left w-full h-full shadow-inner group"
      title={`Voir ${sources.length} sources supplémentaires`}
    >
      <div className="flex items-center justify-between h-full px-1">
        <div className="flex flex-wrap gap-1 max-w-[70%]">
          {previewSources.map((source) => (
            <div key={source.id} className="source-favicon relative">
              <img
                src={`https://www.google.com/s2/favicons?domain=${extractDomain(source.url)}&sz=32`}
                alt={extractDomain(source.url)}
                className="w-3.5 h-3.5 rounded-full border border-gray-600"
                onError={(e) => {
                  // Fallback si l'image ne charge pas
                  (e.target as HTMLImageElement).src = 'data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgc3Ryb2tlPSJjdXJyZW50Q29sb3IiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIiBjbGFzcz0ibHVjaWRlIGx1Y2lkZS1nbG9iZSI+PGNpcmNsZSBjeD0iMTIiIGN5PSIxMiIgcj0iMTAiLz48cGF0aCBkPSJNMiAxMmgyMCIvPjxwYXRoIGQ9Ik0xMiAyYTkuOTQgOS45NCAwIDAgMSA3LjUgMy4yNSIvPjxwYXRoIGQ9Ik0xMiAyYTkuOTQgOS45NCAwIDAgMC03LjUgMy4yNSIvPjxwYXRoIGQ9Ik0xMiAyMmE5Ljk0IDkuOTQgMCAwIDAtNy41LTMuMjUiLz48cGF0aCBkPSJNMTIgMjJhOS45NCA5Ljk0IDAgMCAxIDcuNS0zLjI1Ii8+PHBhdGggZD0iTTEyIDJWMjIiLz48L3N2Zz4=';
                }}
              />
            </div>
          ))}
        </div>
        <div className="font-medium text-xs text-black group-hover:text-tango-600 group-hover:underline">
          +{sources.length}
        </div>
      </div>
    </button>
  );
}
