import React, { useState, useEffect, useRef } from "react";
import { EnhancedSidebarProps } from "../types";
import { Conversation } from "../../ClaudeInterface/types";
import { ModelIconDisplay } from "../../../../features/models";
import ConversationContextMenu from "./ConversationContextMenu";
import SettingsButton from "./SettingsButton";

/**
 * Composant EnhancedSidebar - Version améliorée du composant Sidebar
 * avec fonctionnalité de rétraction/extension
 */
const EnhancedSidebar: React.FC<EnhancedSidebarProps> = ({
  activeButton,
  handleSidebarClick,
  conversations,
  currentConversationId,
  onSelectConversation,
  onNewConversation,
  isCollapsed,
  toggleSidebar,
  navigateToSettings,
  hasMoreConversations,
  isLoadingMore,
  handleShowMore,
  handleScroll,
  conversationListRef,
  menuOpenId,
  isRenaming,
  newTitle,
  confirmDelete,
  settingsMenuOpen,
  renameInputRef,
  handleOpenConversationMenu,
  handleCloseConversationMenu,
  toggleSettingsMenu,
  handleCloseSettingsMenu,
  handleStartRename,
  handleRename,
  setNewTitle,
  handleCancelRename,
  handleConfirmDelete,
  handleDelete,
  handleCancelDelete,
  handleSignOut
}) => {
  // État pour gérer la transition de la sidebar
  const [visualState, setVisualState] = useState(isCollapsed);

  // Mettre à jour l'état visuel avec un délai pour correspondre à l'animation
  useEffect(() => {
    if (isCollapsed) {
      // Si on rétracte, changer immédiatement l'état visuel
      setVisualState(true);
    } else {
      // Si on déploie, attendre que l'animation soit terminée
      const timer = setTimeout(() => {
        setVisualState(false);
      }, 300); // Même durée que l'animation de la sidebar (duration-300)

      return () => clearTimeout(timer);
    }
  }, [isCollapsed]);

  // Formater la date de création d'une conversation
  const formatDate = (timestamp: number) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffSecs = Math.floor(diffMs / 1000);
    const diffMins = Math.floor(diffSecs / 60);
    const diffHours = Math.floor(diffMins / 60);
    const diffDays = Math.floor(diffHours / 24);

    // Si moins d'une minute
    if (diffSecs < 60) {
      return "À l'instant";
    }

    // Si moins d'une heure
    if (diffMins < 60) {
      return `Il y a ${diffMins} ${diffMins === 1 ? 'minute' : 'minutes'}`;
    }

    // Si moins d'un jour
    if (diffHours < 24) {
      return `Il y a ${diffHours} ${diffHours === 1 ? 'heure' : 'heures'}`;
    }

    // Si moins d'une semaine
    if (diffDays < 7) {
      return `Il y a ${diffDays} ${diffDays === 1 ? 'jour' : 'jours'}`;
    }

    // Sinon, afficher la date complète
    const options: Intl.DateTimeFormatOptions = {
      day: 'numeric',
      month: 'long',
      year: 'numeric'
    };

    return date.toLocaleDateString('fr-FR', options);
  };

  // Définir une classe pour les boutons avec une largeur fixe
  // Utiliser visualState pour synchroniser avec l'animation de la sidebar
  const buttonClass = `${visualState ? 'w-8 h-8' : 'w-52 h-8 px-3 mx-auto'} rounded-lg flex items-center transition-all duration-300`;

  // Map pour stocker les références des boutons de menu pour chaque conversation
  const menuButtonRefs = useRef<Map<string, React.RefObject<HTMLButtonElement | null>>>(new Map());

  return (
    <div
      className={`border-r border-claude-light-gray flex flex-col items-center py-4 hidden sm:flex transition-all duration-300 ${
        isCollapsed ? "w-16" : "w-64"
      }`}
    >
      <div className="flex flex-col items-center flex-grow w-full">
        <button
          className="w-8 h-8 mb-6 flex items-center justify-center hover:bg-claude-gray/50 rounded-full transition-colors"
          title={isCollapsed ? "Développer la sidebar" : "Rétracter la sidebar"}
          onClick={toggleSidebar}
        >
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-6 h-6">
            <path strokeLinecap="round" strokeLinejoin="round" d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5" />
          </svg>
        </button>

        <button
          className={`${buttonClass} bg-claude-orange hover:bg-claude-orange/90 text-white justify-center mb-4`}
          onClick={() => {
            handleSidebarClick("new");
            onNewConversation();
          }}
          title="Nouvelle conversation"
        >
          {visualState ? (
            <div className="flex items-center justify-center w-full h-full">
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-5 h-5">
                <path strokeLinecap="round" strokeLinejoin="round" d="M12 4.5v15m7.5-7.5h-15" />
              </svg>
            </div>
          ) : (
            <div className="flex items-center justify-between w-full">
              <div className="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-5 h-5 flex-shrink-0">
                  <path strokeLinecap="round" strokeLinejoin="round" d="M12 4.5v15m7.5-7.5h-15" />
                </svg>
                <span className="ml-2 text-sm font-medium">Nouvelle conversation</span>
              </div>
            </div>
          )}
        </button>



        {/* Liste des conversations récentes (toujours visible) */}
        {conversations.length > 0 && (
          <div
            className={`w-full ${visualState ? 'px-0' : 'px-2'} mt-2 overflow-y-auto max-h-[calc(100vh-200px)]`}
            ref={conversationListRef}
            onScroll={handleScroll}
          >
            {/* Titre de la section */}
            {!visualState && (
              <h3 className="text-xs font-medium text-gray-400 mb-2 px-2">Récents</h3>
            )}

            {/* Liste des conversations */}
            {conversations.map((conversation: Conversation) =>
              isRenaming === conversation._id ? (
                <form
                  key={`rename-${conversation._id}`}
                  onSubmit={(e) => {
                    e.preventDefault();
                    console.log("[EnhancedSidebar] Rename form submitted");
                    console.log("[EnhancedSidebar] conversation._id:", conversation._id);
                    console.log("[EnhancedSidebar] newTitle:", newTitle);
                    if (handleRename) {
                      handleRename(e, conversation._id);
                    } else {
                      console.log("[EnhancedSidebar] handleRename is undefined");
                    }
                  }}
                  className={`${
                    visualState ? "hidden" : "w-full p-3"
                  } mb-2 rounded-md bg-claude-light-gray/20 border border-claude-light-gray/10`}
                  onClick={(e) => e.stopPropagation()}
                >
                  <input
                    ref={renameInputRef}
                    type="text"
                    value={newTitle || ""}
                    onChange={(e) => {
                      console.log("[EnhancedSidebar] Input changed:", e.target.value);
                      if (setNewTitle) {
                        setNewTitle(e.target.value);
                      } else {
                        console.log("[EnhancedSidebar] setNewTitle is undefined");
                      }
                    }}
                    className="w-full p-2 bg-claude-gray border border-claude-light-gray/30 rounded-md text-white text-sm focus:outline-none focus:ring-1 focus:ring-claude-orange/50 focus:border-claude-orange/50"
                    autoFocus
                    placeholder="Nom de la conversation"
                  />
                  <div className="flex justify-end mt-3 space-x-3">
                    <button
                      type="button"
                      className="px-3 py-1.5 text-xs text-gray-400 hover:text-white transition-colors"
                      onClick={() => {
                        console.log("[EnhancedSidebar] Cancel rename button clicked");
                        if (handleCancelRename) {
                          handleCancelRename();
                        } else {
                          console.log("[EnhancedSidebar] handleCancelRename is undefined");
                        }
                      }}
                    >
                      Annuler
                    </button>
                    <button
                      type="submit"
                      className="px-3 py-1.5 text-xs bg-claude-orange text-white rounded-md hover:bg-claude-orange/90 transition-colors"
                    >
                      Renommer
                    </button>
                  </div>
                </form>
              ) : confirmDelete === conversation._id ? (
                <div
                  key={`delete-${conversation._id}`}
                  className={`${
                    visualState ? "hidden" : "w-full p-3"
                  } mb-2 rounded-md bg-claude-light-gray/20 border border-claude-light-gray/10 text-white`}
                  onClick={(e) => e.stopPropagation()}
                >
                  <p className="text-sm text-red-400 mb-3 font-medium">Supprimer cette conversation ?</p>
                  <div className="flex justify-end space-x-3">
                    <button
                      type="button"
                      className="px-3 py-1.5 text-xs text-gray-400 hover:text-white transition-colors"
                      onClick={() => {
                        console.log("[EnhancedSidebar] Cancel delete button clicked");
                        if (handleCancelDelete) {
                          handleCancelDelete();
                        } else {
                          console.log("[EnhancedSidebar] handleCancelDelete is undefined");
                        }
                      }}
                    >
                      Annuler
                    </button>
                    <button
                      type="button"
                      className="px-3 py-1.5 text-xs bg-red-500 text-white rounded-md hover:bg-red-600 transition-colors"
                      onClick={() => {
                        console.log("[EnhancedSidebar] Delete button clicked");
                        console.log("[EnhancedSidebar] conversation._id:", conversation._id);
                        if (handleDelete) {
                          handleDelete(conversation._id);
                        } else {
                          console.log("[EnhancedSidebar] handleDelete is undefined");
                        }
                      }}
                    >
                      Supprimer
                    </button>
                  </div>
                </div>
              ) : (
                <div
                  key={conversation._id}
                  className="conversation-item-wrapper"
                >
                  {/* Utilisation du composant ConversationItem */}
                  <div className="relative group conversation-item">
                    <button
                      className={`${
                        visualState
                          ? "w-12 h-12 mx-auto p-0 flex justify-center"
                          : "w-full p-2"
                      } mb-2 rounded-md text-left text-xs flex items-center ${
                        currentConversationId === conversation._id
                          ? "bg-claude-orange/20 text-white border-l-4 border-claude-orange"
                          : menuOpenId === conversation._id
                            ? "bg-claude-light-gray/30 text-white"
                            : "text-gray-400 hover:bg-claude-light-gray/20"
                      }`}
                      onClick={() => onSelectConversation(conversation._id)}
                      title={conversation.title}
                    >
                      <div className={`${
                        visualState
                          ? "w-8 h-8 min-w-8 m-0"
                          : "w-8 h-8 min-w-8 mr-2"
                      } bg-claude-gray rounded-full flex items-center justify-center`}>
                        <ModelIconDisplay conversation={conversation} size={16} />
                      </div>
                      {!visualState && (
                        <div className="flex-1 truncate">
                          <div className="font-medium truncate">{conversation.title}</div>
                          <div className="text-xs text-gray-500">{formatDate(conversation._creationTime)}</div>
                        </div>
                      )}
                    </button>

                    {/* Bouton de menu contextuel (visible uniquement en mode déployé et au survol) */}
                    {!visualState && (
                      <button
                        ref={(el) => {
                          // Créer une nouvelle référence si elle n'existe pas déjà
                          if (!menuButtonRefs.current.has(conversation._id)) {
                            menuButtonRefs.current.set(conversation._id, React.createRef<HTMLButtonElement | null>());
                          }

                          // Mettre à jour la référence avec l'élément actuel
                          if (el && menuButtonRefs.current.has(conversation._id)) {
                            const ref = menuButtonRefs.current.get(conversation._id);
                            if (ref) {
                              (ref as any).current = el;
                            }
                          }
                        }}
                        className={`absolute right-2 top-1/2 transform -translate-y-1/2 w-8 h-8 rounded-md ${
                          menuOpenId === conversation._id
                            ? "bg-claude-light-gray/50 opacity-100"
                            : "bg-claude-gray opacity-0 group-hover:opacity-100"
                        } flex items-center justify-center transition-all`}
                        onClick={(e) => {
                          console.log("[EnhancedSidebar] Menu button clicked");
                          console.log("[EnhancedSidebar] conversation._id:", conversation._id);
                          if (handleOpenConversationMenu) {
                            handleOpenConversationMenu(e, conversation._id);
                          } else {
                            console.log("[EnhancedSidebar] handleOpenConversationMenu is undefined");
                          }
                        }}
                        aria-label="Menu de la conversation"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
                        </svg>
                      </button>
                    )}

                    {/* Menu contextuel */}
                    {menuOpenId === conversation._id && (
                      <>
                        {console.log("[EnhancedSidebar] Rendering ConversationContextMenu")}
                        {console.log("[EnhancedSidebar] menuButtonRefs:", menuButtonRefs.current.get(conversation._id))}
                        <ConversationContextMenu
                          conversationId={conversation._id}
                          title={conversation.title}
                          isOpen={menuOpenId === conversation._id}
                          onClose={() => {
                            console.log("[EnhancedSidebar] onClose called");
                            if (handleCloseConversationMenu) {
                              handleCloseConversationMenu();
                            } else {
                              console.log("[EnhancedSidebar] handleCloseConversationMenu is undefined");
                            }
                          }}
                          onRename={(id, title) => {
                            console.log("[EnhancedSidebar] onRename called");
                            console.log("[EnhancedSidebar] id:", id);
                            console.log("[EnhancedSidebar] title:", title);
                            if (handleStartRename) {
                              handleStartRename(id, title);
                            } else {
                              console.log("[EnhancedSidebar] handleStartRename is undefined");
                            }
                          }}
                          onDelete={(id) => {
                            console.log("[EnhancedSidebar] onDelete called");
                            console.log("[EnhancedSidebar] id:", id);
                            if (handleConfirmDelete) {
                              handleConfirmDelete(id);
                            } else {
                              console.log("[EnhancedSidebar] handleConfirmDelete is undefined");
                            }
                          }}
                          triggerRef={menuButtonRefs.current.get(conversation._id) || { current: null }}
                        />
                      </>
                    )}
                  </div>
                </div>
              )
            )}

            {/* Bouton "Afficher plus" ou indicateur de chargement */}
            {hasMoreConversations && !visualState && (
              <button
                onClick={handleShowMore}
                disabled={isLoadingMore}
                className="w-full mt-2 py-2 px-3 text-sm text-gray-400 hover:bg-claude-light-gray/20 rounded-lg transition-colors disabled:opacity-50"
              >
                {isLoadingMore ? (
                  <span className="flex items-center justify-center">
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Chargement...
                  </span>
                ) : (
                  "Afficher plus"
                )}
              </button>
            )}
          </div>
        )}
      </div>

      <SettingsButton
        buttonClass={buttonClass}
        visualState={visualState}
        settingsMenuOpen={settingsMenuOpen || false}
        toggleSettingsMenu={toggleSettingsMenu || (() => {})}
        handleCloseSettingsMenu={handleCloseSettingsMenu || (() => {})}
        navigateToSettings={navigateToSettings}
        handleSignOut={handleSignOut || (() => {})}
      />
    </div>
  );
};

export default EnhancedSidebar;
