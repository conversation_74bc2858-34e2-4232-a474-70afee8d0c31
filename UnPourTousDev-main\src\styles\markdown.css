/* Styles pour le contenu Markdown */

/* Styles généraux */
.markdown-content {
  overflow-wrap: break-word;
  word-break: break-word;
  font-size: 0.875rem;
  line-height: 1.5;
  color: white !important;
}

/* Forcer la couleur blanche pour tous les éléments de texte */
.markdown-content p,
.markdown-content li,
.markdown-content span,
.markdown-content div,
.markdown-content h1,
.markdown-content h2,
.markdown-content h3,
.markdown-content h4,
.markdown-content h5,
.markdown-content h6 {
  color: white !important;
}

/* Empêcher les sauts de ligne multiples */
.markdown-content br + br,
.markdown-content br + br + br,
.markdown-content p:empty,
.markdown-content p:empty + p:empty {
  display: none;
}

/* Réduire l'espacement entre les paragraphes */
.markdown-content p + p {
  margin-top: -0.25rem;
}

/* Normalisation de l'espacement vertical - version compacte */
.markdown-content > * {
  margin-top: 0;
  margin-bottom: 0.5rem;
}

.markdown-content > *:last-child {
  margin-bottom: 0;
}

/* Styles pour les titres */
.markdown-content h1,
.markdown-content h2,
.markdown-content h3,
.markdown-content h4,
.markdown-content h5,
.markdown-content h6 {
  font-weight: 600;
  margin-top: 0.75rem;
  margin-bottom: 0.5rem;
  line-height: 1.2;
  color: white !important;
}

.markdown-content h1 {
  font-size: 1.5rem;
}

.markdown-content h2 {
  font-size: 1.25rem;
}

.markdown-content h3 {
  font-size: 1.125rem;
}

.markdown-content h4 {
  font-size: 1rem;
}

/* Styles pour les paragraphes */
.markdown-content p {
  margin-top: 0;
  margin-bottom: 0.5rem;
  line-height: 1.4;
  color: white !important;
}

/* Styles pour les listes */
.markdown-content ul,
.markdown-content ol {
  padding-left: 1.5rem;
  margin-top: 0;
  margin-bottom: 0.5rem;
  color: white !important;
}

.markdown-content li {
  margin-top: 0.125rem;
  margin-bottom: 0.125rem;
  color: white !important;
}

.markdown-content li > p {
  margin-top: 0.125rem;
  margin-bottom: 0.125rem;
  color: white !important;
}

.markdown-content ul {
  list-style-type: disc;
}

.markdown-content ol {
  list-style-type: decimal;
}

/* Styles pour les listes de tâches */
.markdown-content ul.contains-task-list {
  list-style-type: none;
  padding-left: 0.5rem;
}

.markdown-content .task-list-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

/* Styles pour les liens */
.markdown-content a {
  color: #e37314;
  text-decoration: none;
}

.markdown-content a:hover {
  text-decoration: underline;
}

/* Styles pour les citations */
.markdown-content blockquote {
  border-left: 4px solid #e2e8f0;
  padding-left: 1rem;
  margin: 0.5rem 0;
  color: white !important;
}

.dark .markdown-content blockquote {
  border-left-color: #4a5568;
  color: white !important;
}

/* Styles pour les images */
.markdown-content img {
  max-width: 100%;
  height: auto;
  margin: 0.5rem 0;
  border-radius: 0.375rem;
}

/* Styles pour les références */
.markdown-content .reference-link {
  color: #e37314;
  font-weight: 500;
}

.markdown-content .reference-link:hover {
  text-decoration: underline;
}

/* Styles pour les tableaux */
.markdown-content table {
  width: 100%;
  border-collapse: collapse;
  margin: 0.5rem 0;
}

.markdown-content table th,
.markdown-content table td {
  padding: 0.375rem 0.5rem;
  text-align: left;
}

.markdown-content table th {
  font-weight: 600;
}

/* Styles pour le code */
.markdown-content pre {
  margin: 0.5rem 0;
  border-radius: 0.375rem;
  overflow: hidden;
}

.markdown-content code {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
}

.markdown-content :not(pre) > code {
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  font-size: 0.875rem;
}

/* Styles pour les séparateurs horizontaux */
.markdown-content hr {
  margin: 0.75rem 0;
  border: 0;
  border-top: 1px solid #e2e8f0;
}

.dark .markdown-content hr {
  border-top-color: #4a5568;
}

/* Styles pour les diagrammes Mermaid */
.markdown-content .mermaid {
  text-align: center;
  margin: 0.5rem 0;
  background-color: white;
  padding: 0.5rem;
  border-radius: 0.375rem;
}

.dark .markdown-content .mermaid {
  background-color: #1a202c;
}
