import React, { useState, useEffect } from "react";
import { ModelSelectorProps, AIModel } from "../types";
import ModelIcon from "../../../../features/models/ModelIcon";
import { AUTOSELECT_ID } from "../hooks/useModelSelection";

/**
 * Composant ModelSelector pour l'interface Claude
 * Affiche les modèles disponibles dans chaque catégorie avec leurs icônes correspondantes
 */
const ModelSelector: React.FC<ModelSelectorProps> = ({
  models,
  selectedModel,
  setSelectedModel,
  openCategory
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [filteredModels, setFilteredModels] = useState<AIModel[]>([]);

  // Filtrer les modèles par catégorie
  useEffect(() => {
    if (!openCategory) {
      setFilteredModels([]);
      return;
    }

    // Ajouter l'option AutoSelect en premier
    const autoRouterModel = models.find((model: AIModel) => model.modelId === AUTOSELECT_ID);

    // Filtrer les modèles par catégorie
    console.log("[ModelSelector] Filtrage pour catégorie:", openCategory);
    console.log("[ModelSelector] Modèles disponibles:", models);

    const categoryModels = models.filter((model: AIModel) => {
      console.log(`[ModelSelector] Modèle ${model.name}:`, {
        chat: model.chat,
        webSearch: model.webSearch,
        reasoning: model.reasoning,
        enabled: model.enabled
      });

      if (openCategory === "chat") return model.chat === true && model.enabled !== false;
      if (openCategory === "web_search") return model.webSearch === true && model.enabled !== false;
      if (openCategory === "reasoning") return model.reasoning === true && model.enabled !== false;
      return false;
    });

    console.log("[ModelSelector] Modèles filtrés:", categoryModels);

    // Trier les modèles par nom
    categoryModels.sort((a, b) => a.name.localeCompare(b.name));

    // Ajouter l'option AutoSelect en premier si elle existe
    if (autoRouterModel) {
      setFilteredModels([autoRouterModel, ...categoryModels]);
    } else {
      setFilteredModels(categoryModels);
    }
  }, [openCategory, models]);

  // Trouver le modèle sélectionné
  const selectedModelInfo = models.find((model: AIModel) => model.modelId === selectedModel);

  // Obtenir le titre de la catégorie
  const getCategoryTitle = () => {
    switch (openCategory) {
      case "chat": return "Modèles de discussion";
      case "web_search": return "Modèles avec recherche web";
      case "reasoning": return "Modèles de raisonnement";
      default: return "Sélectionner un modèle";
    }
  };

  return (
    <div className="relative w-full">
      <div className="flex flex-wrap justify-center gap-3 mt-3">
        {filteredModels.map((model: AIModel) => (
          <button
            key={model.modelId}
            onClick={() => setSelectedModel(model.modelId)}
            className={`flex items-center p-2 rounded-lg transition-all duration-200 ${
              model.modelId === selectedModel
                ? "bg-primary/30 text-white border-2 border-primary shadow-md transform scale-105"
                : "bg-claude-gray text-gray-300 border border-claude-light-gray hover:bg-claude-light-gray/30 hover:scale-105"
            }`}
            title={model.description || model.name}
          >
            <ModelIcon
              model={model.modelId}
              provider={model.provider}
              size={24}
              className="mr-2"
            />
            <span className="font-medium text-sm whitespace-nowrap">{model.name}</span>
          </button>
        ))}
      </div>

      {filteredModels.length === 0 && (
        <div className="text-center py-3 text-gray-400 text-sm">
          Aucun modèle disponible dans cette catégorie
        </div>
      )}
    </div>
  );
};

export default ModelSelector;
