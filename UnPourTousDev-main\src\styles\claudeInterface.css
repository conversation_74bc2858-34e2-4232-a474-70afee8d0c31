/* Loader animation for the Claude interface */
.loader {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: inline-block;
  position: relative;
  border: 2px solid #FF8E6E;
  animation: rotation 1s linear infinite;
}

.loader:after {
  content: '';
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: 2px solid;
  border-color: #5D5CDE transparent;
}

@keyframes rotation {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Custom scrollbar styles */
.claude-interface ::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.claude-interface ::-webkit-scrollbar-track {
  background: transparent;
}

.claude-interface ::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.15);
  border-radius: 3px;
}

.claude-interface ::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}

.claude-interface.dark ::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.15);
}

.claude-interface.dark ::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* Font styles */
.claude-interface h1 {
  font-family: var(--font-display);
  font-weight: 400; /* Regular font weight for the main heading */
}

.claude-interface textarea {
  font-family: inherit;
}

/* Ensure smooth transitions */
.claude-interface button {
  transition: all 0.2s ease;
}

/* Add some letter spacing to the heading */
.claude-interface h1 {
  letter-spacing: 0.01em;
  line-height: 1.2;
}

/* Ensure proper textarea behavior */
.claude-interface textarea {
  line-height: 1.5;
  overflow-y: hidden; /* Hide scrollbar when not needed */
}
