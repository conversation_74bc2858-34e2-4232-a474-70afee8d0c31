import React from "react";
import { ModelSelectorProps } from "../types";
import { ModelIcon } from "../../../../features/models";

/**
 * Composant ModelSelector pour l'interface Comfy
 * Affiche la liste des modèles disponibles pour la catégorie sélectionnée
 */
const ModelSelector: React.FC<ModelSelectorProps> = ({
  models,
  selectedModel,
  setSelectedModel,
  openCategory
}) => {
  // Filtrer les modèles par catégorie
  const filteredModels = models.filter(model => {
    // CORRECTION : Accepter les modèles même avec enabled: false pour les tests
    // if (model.enabled === false) return false;

    switch (openCategory) {
      case "chat":
        return model.chat === true;
      case "web_search":
        return model.webSearch === true;
      case "reasoning":
        return model.reasoning === true;
      default:
        return false;
    }
  });

  if (filteredModels.length === 0) {
    return (
      <div className="bg-claude-gray rounded-lg p-4 text-center">
        <p className="text-gray-400 text-sm">Aucun modèle disponible pour cette catégorie</p>
      </div>
    );
  }

  return (
    <div className="bg-claude-gray rounded-lg p-4">
      <h3 className="text-white font-medium mb-3 text-sm">
        Modèles disponibles - {openCategory === "chat" ? "Discussion" :
                              openCategory === "web_search" ? "Recherche Web" :
                              "Raisonnement"}
      </h3>
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-2">
        {filteredModels.map((model) => (
          <button
            key={model.modelId}
            onClick={() => setSelectedModel(model.modelId)}
            className={`p-3 rounded-lg border transition-all text-left ${
              selectedModel === model.modelId
                ? "bg-claude-orange border-claude-orange text-white shadow-md"
                : "bg-claude-light-gray border-claude-light-gray text-white hover:bg-claude-light-gray/80"
            }`}
          >
            <div className="flex items-center gap-2 mb-1">
              <ModelIcon
                model={model.modelId}
                provider={model.provider}
                size={16}
                className="flex-shrink-0"
              />
              <span className="font-medium text-sm truncate">{model.name}</span>
            </div>
            <div className="text-xs text-gray-300 truncate">
              {model.webSearch && (
                <span className="inline-flex items-center px-1.5 py-0.5 rounded-full text-xs bg-blue-100 text-blue-800">
                  Web
                </span>
              )}
            </div>
          </button>
        ))}
      </div>
    </div>
  );
};

export default ModelSelector;
