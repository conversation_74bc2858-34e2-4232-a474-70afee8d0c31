import React from "react";
import { ModelSelectorProps } from "../types";
import { ModelIcon } from "../../../../features/models";

/**
 * Composant ModelSelector pour l'interface Comfy
 * Affiche la liste des modèles disponibles pour la catégorie sélectionnée
 */
const ModelSelector: React.FC<ModelSelectorProps> = ({
  models,
  selectedModel,
  setSelectedModel,
  openCategory
}) => {
  // Filtrer les modèles par catégorie
  const filteredModels = models.filter(model => {
    // CORRECTION : Accepter les modèles même avec enabled: false pour les tests
    // if (model.enabled === false) return false;

    switch (openCategory) {
      case "chat":
        return model.chat === true;
      case "web_search":
        return model.webSearch === true;
      case "reasoning":
        return model.reasoning === true;
      default:
        return false;
    }
  });

  if (filteredModels.length === 0) {
    return (
      <div className="bg-claude-gray rounded-lg p-4 text-center">
        <p className="text-gray-400 text-sm">Aucun modèle disponible pour cette catégorie</p>
      </div>
    );
  }

  return (
    <div className="bg-claude-gray/50 rounded-xl p-3 border border-claude-light-gray/30 backdrop-blur-sm">
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
        {filteredModels.map((model) => (
          <button
            key={model.modelId}
            onClick={() => setSelectedModel(model.modelId)}
            className={`group relative p-4 rounded-xl border transition-all duration-300 text-left overflow-hidden ${
              selectedModel === model.modelId
                ? "bg-gradient-to-br from-claude-orange to-claude-orange/80 border-claude-orange text-white shadow-lg shadow-claude-orange/25 scale-105"
                : "bg-claude-light-gray/60 border-claude-light-gray/40 text-white hover:bg-claude-light-gray/80 hover:border-claude-light-gray/60 hover:scale-102 hover:shadow-md"
            }`}
          >
            {/* Effet de brillance au survol */}
            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-700"></div>

            <div className="relative z-10">
              <div className="flex items-center gap-3 mb-2">
                <div className={`p-1.5 rounded-lg transition-colors ${
                  selectedModel === model.modelId
                    ? "bg-white/20"
                    : "bg-claude-light-gray/40 group-hover:bg-claude-light-gray/60"
                }`}>
                  <ModelIcon
                    model={model.modelId}
                    provider={model.provider}
                    size={18}
                    className="flex-shrink-0"
                  />
                </div>
                <span className="font-semibold text-sm truncate">{model.name}</span>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  {model.webSearch && (
                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-500/20 text-blue-300 border border-blue-500/30">
                      <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                      </svg>
                      Web
                    </span>
                  )}
                  {model.reasoning && (
                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-500/20 text-purple-300 border border-purple-500/30">
                      <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                      </svg>
                      Raisonnement
                    </span>
                  )}
                </div>

                {selectedModel === model.modelId && (
                  <div className="flex items-center">
                    <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                  </div>
                )}
              </div>
            </div>
          </button>
        ))}
      </div>
    </div>
  );
};

export default ModelSelector;
