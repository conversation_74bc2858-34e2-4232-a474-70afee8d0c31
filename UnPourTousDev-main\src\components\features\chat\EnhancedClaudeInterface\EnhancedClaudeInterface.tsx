import React, { useState, useRef } from "react";
import { useNavigate } from "react-router-dom";
import { EnhancedClaudeInterfaceProps } from "./types";
import { useChat } from "../ClaudeInterface/hooks/useChat";
import { useModelSelection, AUTOSELECT_ID } from "../ClaudeInterface/hooks/useModelSelection";
import useEnhancedSidebar from "./hooks/useEnhancedSidebar";
import EnhancedSidebar from "./components/EnhancedSidebar";
import ChatArea from "../ClaudeInterface/components/ChatArea";
import MessageInput from "../ClaudeInterface/components/MessageInput";
import ModelSelector from "../ClaudeInterface/components/ModelSelector";
import logo from "../../../../assets/logo3.svg";

/**
 * Composant EnhancedClaudeInterface - Version améliorée du composant ClaudeInterface
 * avec sidebar rétractable et zone de chat centrée
 */
const EnhancedClaudeInterface: React.FC<EnhancedClaudeInterfaceProps> = ({
  onConversationCreated
}) => {
  const navigate = useNavigate();

  // État pour gérer la rétraction de la sidebar (rétractée par défaut)
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(true);

  // Utiliser les hooks personnalisés
  const {
    userMessage,
    setUserMessage,
    chatStarted,
    assistantResponse,
    isLoading,
    currentConversationId,
    activeButton,
    setActiveButton,
    bottomRef,
    conversations,
    handleSidebarClick,
    handleSelectConversation,
    handleNewConversation,
    handleKeyDown,
    startChat: originalStartChat
  } = useChat(onConversationCreated);

  // Définir l'onglet "chat" comme actif par défaut pour afficher les conversations
  React.useEffect(() => {
    setActiveButton("chat");
  }, []);

  // Utiliser le hook pour la sidebar améliorée
  const {
    conversations: limitedConversations,
    hasMoreConversations,
    isLoadingMore,
    conversationListRef,
    renameInputRef,
    handleShowMore,
    handleScroll,
    menuOpenId,
    isRenaming,
    newTitle,
    confirmDelete,
    settingsMenuOpen,
    handleOpenConversationMenu,
    handleCloseConversationMenu,
    toggleSettingsMenu,
    handleCloseSettingsMenu,
    handleStartRename,
    handleRename,
    setNewTitle,
    handleCancelRename,
    handleConfirmDelete,
    handleDelete,
    handleCancelDelete,
    handleSignOut
  } = useEnhancedSidebar(currentConversationId, handleSelectConversation, handleNewConversation);

  const {
    selectedModel,
    setSelectedModel,
    useAutoRouter,
    setUseAutoRouter,
    openCategory,
    toggleCategory,
    models,
    activeSelection,
    handleAutoSelect,
    handleModelSelect
  } = useModelSelection();

  // Fonction pour basculer l'état de la sidebar
  const toggleSidebar = () => {
    setIsSidebarCollapsed(!isSidebarCollapsed);
  };

  // Fonction pour naviguer vers la page de paramètres
  const navigateToSettings = () => {
    navigate("/settings");
  };

  // Fonction pour démarrer le chat avec le modèle sélectionné
  const startChat = (modelIdFromInput?: string) => {
    // Log de diagnostic pour voir la valeur de activeSelection et modelIdFromInput
    console.log("[EnhancedClaudeInterface.tsx] activeSelection dans EnhancedClaudeInterface:", activeSelection);
    console.log("[EnhancedClaudeInterface.tsx] modelIdFromInput reçu de MessageInput:", modelIdFromInput);
    console.log("[EnhancedClaudeInterface.tsx] AUTOSELECT_ID dans EnhancedClaudeInterface:", AUTOSELECT_ID);

    // Utiliser modelIdFromInput s'il est fourni, sinon utiliser activeSelection
    const modelIdToUse = modelIdFromInput || activeSelection;
    console.log("[EnhancedClaudeInterface.tsx] modelIdToUse final:", modelIdToUse);

    // Vérifier si modelIdToUse est différent de AUTOSELECT_ID
    if (modelIdToUse !== AUTOSELECT_ID) {
      // Si un modèle spécifique est sélectionné, utiliser son ID
      console.log("[EnhancedClaudeInterface.tsx] Modèle spécifique sélectionné:", modelIdToUse);
      originalStartChat(modelIdToUse);
    } else {
      // Si AutoSelect est sélectionné, passer explicitement AUTOSELECT_ID
      console.log("[EnhancedClaudeInterface.tsx] AutoSelect sélectionné, utilisation de:", AUTOSELECT_ID);
      originalStartChat(AUTOSELECT_ID);
    }
  };

  // Rendu de l'écran de bienvenue
  const renderWelcomeScreen = () => (
    <div className="flex-1 flex flex-col items-center justify-center p-4">
      <div className="flex justify-center mb-6">
        <img src={logo} alt="UnPourTous Logo" className="w-24 h-24" />
      </div>
      <p className="text-gray-400 text-center text-lg font-body mb-4">Je suis là pour discuter, aider ou simplement écouter</p>

      {/* Zone de saisie centrée */}
      <div className="w-full max-w-3xl mx-auto mt-4">
        <MessageInput
          userMessage={userMessage}
          setUserMessage={setUserMessage}
          isLoading={isLoading}
          handleKeyDown={handleKeyDown}
          startChat={startChat}
          selectedModel={selectedModel}
          setSelectedModel={setSelectedModel}
          useAutoRouter={useAutoRouter}
          setUseAutoRouter={setUseAutoRouter}
          // Ces props sont passées pour la compatibilité mais ne sont pas utilisées directement
          // car le composant MessageInput utilise son propre hook useModelSelection
        />
      </div>
    </div>
  );

  // Rendu de l'écran de chat
  const renderChatScreen = () => (
    <div className="flex-1 overflow-y-auto p-4 space-y-6">
      <div className="max-w-3xl mx-auto">
        {userMessage && (
          <div className="mb-6">
            <div className="bg-claude-light-gray/20 rounded-xl p-4">
              <div className="font-medium mb-1 text-gray-400 font-body">Vous</div>
              <div className="text-white font-body">{userMessage || "Comment puis-je vous aider ?"}</div>
            </div>
          </div>
        )}
        {(assistantResponse || isLoading) && (
          <div>
            <div className="bg-claude-light-gray/45 rounded-xl p-4">
              <div className="font-medium mb-1 text-gray-400 font-body">Assistant IA</div>
              {isLoading ? (
                <div className="text-white font-body">
                  <span className="animate-pulse">...</span>
                </div>
              ) : (
                <div className="text-white font-body">{assistantResponse}</div>
              )}
            </div>
          </div>
        )}
        <div ref={bottomRef} />
      </div>
    </div>
  );

  return (
    <div className="claude-interface h-screen flex flex-col bg-claude-dark text-white">
      <div className="flex flex-1 overflow-hidden">
        {/* Sidebar améliorée */}
        <EnhancedSidebar
          activeButton={activeButton}
          handleSidebarClick={handleSidebarClick}
          conversations={limitedConversations}
          currentConversationId={currentConversationId}
          onSelectConversation={handleSelectConversation}
          onNewConversation={handleNewConversation}
          isCollapsed={isSidebarCollapsed}
          toggleSidebar={toggleSidebar}
          navigateToSettings={navigateToSettings}
          hasMoreConversations={hasMoreConversations}
          isLoadingMore={isLoadingMore}
          handleShowMore={handleShowMore}
          handleScroll={handleScroll}
          conversationListRef={conversationListRef}
          menuOpenId={menuOpenId}
          isRenaming={isRenaming}
          newTitle={newTitle}
          confirmDelete={confirmDelete}
          settingsMenuOpen={settingsMenuOpen}
          renameInputRef={renameInputRef}
          handleOpenConversationMenu={handleOpenConversationMenu}
          handleCloseConversationMenu={handleCloseConversationMenu}
          toggleSettingsMenu={toggleSettingsMenu}
          handleCloseSettingsMenu={handleCloseSettingsMenu}
          handleStartRename={handleStartRename}
          handleRename={handleRename}
          setNewTitle={setNewTitle}
          handleCancelRename={handleCancelRename}
          handleConfirmDelete={handleConfirmDelete}
          handleDelete={handleDelete}
          handleCancelDelete={handleCancelDelete}
          handleSignOut={handleSignOut}
        />

        {/* Main Content */}
        <div className={`flex-1 flex flex-col overflow-hidden transition-all duration-300 ${isSidebarCollapsed ? "ml-0" : ""}`}>
          {/* Chat Area */}
          <ChatArea
            chatStarted={chatStarted}
            conversationId={currentConversationId}
            userMessage={userMessage}
            assistantResponse={assistantResponse}
            isLoading={isLoading}
            renderWelcomeScreen={renderWelcomeScreen}
            renderChatScreen={renderChatScreen}
          />

          {/* Model Selector (visible uniquement si une catégorie est ouverte) */}
          {openCategory && (
            <div className="p-4 border-t border-claude-light-gray">
              <div className="max-w-3xl mx-auto">
                <ModelSelector
                  models={models}
                  selectedModel={activeSelection}
                  setSelectedModel={handleModelSelect}
                  openCategory={openCategory}
                />
              </div>
            </div>
          )}

          {/* Message Input (visible uniquement si l'écran de bienvenue n'est pas affiché) */}
          {chatStarted && (
            <div className="p-4 border-t border-claude-light-gray">
              <div className="max-w-3xl mx-auto">
                <MessageInput
                  userMessage={userMessage}
                  setUserMessage={setUserMessage}
                  isLoading={isLoading}
                  handleKeyDown={handleKeyDown}
                  startChat={startChat}
                  selectedModel={selectedModel}
                  setSelectedModel={setSelectedModel}
                  useAutoRouter={useAutoRouter}
                  setUseAutoRouter={setUseAutoRouter}
                  // Ces props sont passées pour la compatibilité mais ne sont pas utilisées directement
                  // car le composant MessageInput utilise son propre hook useModelSelection
                />
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default EnhancedClaudeInterface;
