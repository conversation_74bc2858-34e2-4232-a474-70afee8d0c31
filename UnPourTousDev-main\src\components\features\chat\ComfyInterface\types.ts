import { Id } from "../../../../../convex/_generated/dataModel";

/**
 * Interface pour les propriétés du composant ComfyInterface
 */
export interface ComfyInterfaceProps {
  onConversationCreated?: (id: Id<"conversations">) => void;
}

/**
 * Interface pour les propriétés du composant Sidebar
 */
export interface SidebarProps {
  activeButton: string;
  handleSidebarClick: (buttonType: string) => void;
  conversations: any[];
  currentConversationId: Id<"conversations"> | null;
  onSelectConversation: (id: Id<"conversations">) => void;
  onNewConversation: () => void;
}

/**
 * Interface pour les propriétés du composant ChatArea
 */
export interface ChatAreaProps {
  chatStarted: boolean;
  conversationId: Id<"conversations"> | null;
  userMessage: string;
  assistantResponse: string;
  isLoading: boolean;
  renderWelcomeScreen: () => JSX.Element;
  renderChatScreen: () => JSX.Element;
}

/**
 * Interface pour les propriétés du composant MessageInput
 */
export interface MessageInputProps {
  userMessage: string;
  setUserMessage: (message: string) => void;
  isLoading: boolean;
  handleKeyDown: (e: React.KeyboardEvent) => void;
  startChat: (modelId?: string) => void;
  // Ces props sont conservées pour la compatibilité avec les composants existants
  // mais ne sont plus utilisées directement dans le composant
  selectedModel: string;
  setSelectedModel: (modelId: string) => void;
  useAutoRouter: boolean;
  setUseAutoRouter: (use: boolean) => void;
}

/**
 * Interface pour les propriétés du composant CategoryButtons
 */
export interface CategoryButtonsProps {
  openCategory: string | null;
  toggleCategory: (category: string) => void;
}

/**
 * Interface pour les propriétés du composant ModelSelector
 */
export interface ModelSelectorProps {
  models: any[];
  selectedModel: string;
  setSelectedModel: (modelId: string) => void;
  openCategory: string | null;
}

/**
 * Interface pour une conversation
 */
export interface Conversation {
  _id: Id<"conversations">;
  _creationTime: number;
  title: string;
  userId: string;
  modelId?: string;
  usesAutoRouter?: boolean;
}

/**
 * Interface pour un modèle
 */
export interface Model {
  _id: Id<"livemodels">;
  modelId: string;
  name: string;
  provider: string;
  category: "chat" | "web_search" | "reasoning";
  webSearch?: boolean;
  isActive: boolean;
  iconPath?: string;
}

/**
 * Interface pour les propriétés du composant LargeAutoRouterButton
 */
export interface LargeAutoRouterButtonProps {
  activeSelection: string;
  handleAutoSelect: () => void;
}
